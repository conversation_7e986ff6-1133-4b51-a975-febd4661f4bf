package main

import (
	"bytes"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"os"
	"os/signal"
	"strings"
	"time"

	"github.com/gorilla/websocket"

	"websocket-hub/internal/protocol"
)

var (
	serverAddr = flag.String("addr", "localhost:8080", "WebSocket server address")
	tags       = flag.String("tags", "", "Comma-separated list of tags")
	deviceID   = flag.String("device", "", "Device ID (if empty, will request new one)")
)

type Client struct {
	conn     *websocket.Conn
	deviceID string
	tags     []string
	done     chan struct{}
}

func main() {
	flag.Parse()

	client := &Client{
		done: make(chan struct{}),
	}

	// 解析标签
	if *tags != "" {
		client.tags = strings.Split(*tags, ",")
		for i, tag := range client.tags {
			client.tags[i] = strings.TrimSpace(tag)
		}
	}

	// 如果没有提供设备ID，先请求生成一个
	if *deviceID == "" {
		var err error
		client.deviceID, err = client.requestDeviceID()
		if err != nil {
			log.Fatalf("Failed to get device ID: %v", err)
		}
		fmt.Printf("Generated device ID: %s\n", client.deviceID)
	} else {
		client.deviceID = *deviceID
	}

	// 连接WebSocket服务器
	if err := client.connect(); err != nil {
		log.Fatalf("Failed to connect: %v", err)
	}
	defer client.conn.Close()

	fmt.Printf("Connected to %s with device ID: %s\n", *serverAddr, client.deviceID)
	if len(client.tags) > 0 {
		fmt.Printf("Tags: %v\n", client.tags)
	}

	// 启动消息处理协程
	go client.readMessages()
	go client.handleUserInput()

	// 等待中断信号
	interrupt := make(chan os.Signal, 1)
	signal.Notify(interrupt, os.Interrupt)

	select {
	case <-interrupt:
		fmt.Println("\nReceived interrupt signal, closing connection...")
	case <-client.done:
		fmt.Println("Connection closed")
	}

	// 发送断开连接消息
	disconnectMsg := protocol.NewMessage(protocol.MsgTypeDisconnect, nil)
	if data, err := disconnectMsg.Marshal(); err == nil {
		client.conn.WriteMessage(websocket.BinaryMessage, data)
	}

	// 优雅关闭连接
	client.conn.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, ""))
	select {
	case <-client.done:
	case <-time.After(time.Second):
	}
}

// requestDeviceID 请求生成设备ID
func (c *Client) requestDeviceID() (string, error) {
	reqData := map[string]interface{}{
		"tags": c.tags,
	}

	jsonData, err := json.Marshal(reqData)
	if err != nil {
		return "", err
	}

	resp, err := http.Post(
		fmt.Sprintf("http://%s/api/v1/devices", *serverAddr),
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("HTTP %d", resp.StatusCode)
	}

	var result struct {
		DeviceID string `json:"device_id"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return "", err
	}

	return result.DeviceID, nil
}

// connect 连接到WebSocket服务器
func (c *Client) connect() error {
	u := url.URL{Scheme: "ws", Host: *serverAddr, Path: "/api/v1/ws"}
	
	conn, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		return err
	}
	c.conn = conn

	// 发送握手消息
	handshakePayload := protocol.HandshakePayload{
		DeviceID:   c.deviceID,
		Tags:       c.tags,
		ClientInfo: "Go WebSocket Client v1.0",
	}

	payloadData, err := json.Marshal(handshakePayload)
	if err != nil {
		return err
	}

	handshakeMsg := protocol.NewMessage(protocol.MsgTypeHandshake, payloadData)
	data, err := handshakeMsg.Marshal()
	if err != nil {
		return err
	}

	if err := c.conn.WriteMessage(websocket.BinaryMessage, data); err != nil {
		return err
	}

	// 等待握手响应
	_, respData, err := c.conn.ReadMessage()
	if err != nil {
		return err
	}

	var respMsg protocol.Message
	if err := respMsg.Unmarshal(respData); err != nil {
		return err
	}

	if respMsg.GetType() != protocol.MsgTypeHandshakeResp {
		return fmt.Errorf("unexpected handshake response type: %v", respMsg.GetType())
	}

	var handshakeResp map[string]interface{}
	if err := json.Unmarshal(respMsg.GetPayload(), &handshakeResp); err != nil {
		return err
	}

	if status, ok := handshakeResp["status"].(string); !ok || status != "connected" {
		return fmt.Errorf("handshake failed: %v", handshakeResp)
	}

	return nil
}

// readMessages 读取消息
func (c *Client) readMessages() {
	defer close(c.done)

	for {
		_, data, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket error: %v", err)
			}
			return
		}

		var msg protocol.Message
		if err := msg.Unmarshal(data); err != nil {
			log.Printf("Failed to unmarshal message: %v", err)
			continue
		}

		switch msg.GetType() {
		case protocol.MsgTypeHeartbeat:
			// 响应心跳
			heartbeatResp := protocol.NewMessage(protocol.MsgTypeHeartbeat, nil)
			if respData, err := heartbeatResp.Marshal(); err == nil {
				c.conn.WriteMessage(websocket.BinaryMessage, respData)
			}

		case protocol.MsgTypeUnicast, protocol.MsgTypeBroadcast, protocol.MsgTypeTagcast:
			fmt.Printf("\n[%s] Received message: %s\n> ", 
				time.Now().Format("15:04:05"), 
				string(msg.GetPayload()))

		case protocol.MsgTypeError:
			var errorPayload protocol.ErrorPayload
			if err := json.Unmarshal(msg.GetPayload(), &errorPayload); err == nil {
				fmt.Printf("\n[ERROR] %s (Code: %d)\n> ", errorPayload.Message, errorPayload.Code)
			}

		default:
			fmt.Printf("\n[%s] Unknown message type: %v\n> ", 
				time.Now().Format("15:04:05"), 
				msg.GetType())
		}
	}
}

// handleUserInput 处理用户输入
func (c *Client) handleUserInput() {
	fmt.Println("\nCommands:")
	fmt.Println("  send <target_device_id> <message>  - Send unicast message")
	fmt.Println("  broadcast <message>                - Send broadcast message")
	fmt.Println("  tagcast <tag1,tag2> <message>      - Send tagcast message")
	fmt.Println("  quit                               - Quit")
	fmt.Print("> ")

	for {
		var input string
		fmt.Scanln(&input)

		if input == "quit" {
			close(c.done)
			return
		}

		parts := strings.SplitN(input, " ", 3)
		if len(parts) < 2 {
			fmt.Print("> ")
			continue
		}

		command := parts[0]
		var msg *protocol.Message

		switch command {
		case "send":
			if len(parts) < 3 {
				fmt.Println("Usage: send <target_device_id> <message>")
				fmt.Print("> ")
				continue
			}
			targetID := parts[1]
			message := parts[2]
			
			payload := protocol.UnicastPayload{
				TargetDeviceID: targetID,
				Data:           []byte(message),
			}
			payloadData, _ := json.Marshal(payload)
			msg = protocol.NewMessage(protocol.MsgTypeUnicast, payloadData)

		case "broadcast":
			message := parts[1]
			payload := protocol.BroadcastPayload{
				Data: []byte(message),
			}
			payloadData, _ := json.Marshal(payload)
			msg = protocol.NewMessage(protocol.MsgTypeBroadcast, payloadData)

		case "tagcast":
			if len(parts) < 3 {
				fmt.Println("Usage: tagcast <tag1,tag2> <message>")
				fmt.Print("> ")
				continue
			}
			tagsStr := parts[1]
			message := parts[2]
			tags := strings.Split(tagsStr, ",")
			for i, tag := range tags {
				tags[i] = strings.TrimSpace(tag)
			}
			
			payload := protocol.TagcastPayload{
				Tags: tags,
				Data: []byte(message),
			}
			payloadData, _ := json.Marshal(payload)
			msg = protocol.NewMessage(protocol.MsgTypeTagcast, payloadData)

		default:
			fmt.Printf("Unknown command: %s\n", command)
			fmt.Print("> ")
			continue
		}

		if msg != nil {
			data, err := msg.Marshal()
			if err != nil {
				fmt.Printf("Failed to marshal message: %v\n", err)
			} else {
				err = c.conn.WriteMessage(websocket.BinaryMessage, data)
				if err != nil {
					fmt.Printf("Failed to send message: %v\n", err)
				}
			}
		}

		fmt.Print("> ")
	}
}
