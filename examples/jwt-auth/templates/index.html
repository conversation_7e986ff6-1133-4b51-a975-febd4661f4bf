<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, button {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .token-display {
            word-break: break-all;
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>JWT Authentication Example</h1>
    <p>This demo shows how to generate and validate JWT tokens for WebSocket authentication.</p>

    <!-- Generate Token Section -->
    <div class="container">
        <h2>Generate JWT Token</h2>
        <form id="generateForm">
            <div class="form-group">
                <label for="deviceId">Device ID (optional):</label>
                <input type="text" id="deviceId" placeholder="e.g., device-123">
            </div>
            <div class="form-group">
                <label for="tags">Tags (comma-separated):</label>
                <input type="text" id="tags" placeholder="e.g., user,mobile,premium">
            </div>
            <div class="form-group">
                <label for="expiry">Expiry (seconds):</label>
                <input type="number" id="expiry" value="3600" placeholder="3600">
            </div>
            <button type="submit">Generate Token</button>
        </form>
        <div id="generateResult" class="result" style="display: none;"></div>
    </div>

    <!-- Validate Token Section -->
    <div class="container">
        <h2>Validate JWT Token</h2>
        <form id="validateForm">
            <div class="form-group">
                <label for="validateToken">JWT Token:</label>
                <textarea id="validateToken" rows="3" placeholder="Paste JWT token here..."></textarea>
            </div>
            <button type="submit">Validate Token</button>
        </form>
        <div id="validateResult" class="result" style="display: none;"></div>
    </div>

    <!-- WebSocket Test Section -->
    <div class="container">
        <h2>WebSocket Connection Test</h2>
        <form id="wsTestForm">
            <div class="form-group">
                <label for="wsUrl">WebSocket URL:</label>
                <input type="text" id="wsUrl" value="ws://localhost:8080/api/v1/ws" placeholder="ws://localhost:8080/api/v1/ws">
            </div>
            <div class="form-group">
                <label for="wsToken">JWT Token:</label>
                <textarea id="wsToken" rows="3" placeholder="Paste JWT token here..."></textarea>
            </div>
            <div class="form-group">
                <label for="wsTags">Tags (comma-separated):</label>
                <input type="text" id="wsTags" placeholder="e.g., user,mobile">
            </div>
            <button type="button" id="wsConnect">Connect</button>
            <button type="button" id="wsDisconnect" disabled>Disconnect</button>
        </form>
        <div id="wsResult" class="result" style="display: none;"></div>
    </div>

    <script>
        let ws = null;

        // Generate Token
        document.getElementById('generateForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const deviceId = document.getElementById('deviceId').value;
            const tags = document.getElementById('tags').value.split(',').map(t => t.trim()).filter(t => t);
            const expiry = parseInt(document.getElementById('expiry').value) || 3600;
            
            try {
                const response = await fetch('/api/v1/token/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        device_id: deviceId || undefined,
                        tags: tags,
                        expiry: expiry
                    })
                });
                
                const result = await response.json();
                const resultDiv = document.getElementById('generateResult');
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `Token generated successfully!\n\n${JSON.stringify(result, null, 2)}`;
                    
                    // Auto-fill validate form
                    document.getElementById('validateToken').value = result.token;
                    document.getElementById('wsToken').value = result.token;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `Error: ${result.error}`;
                }
                
                resultDiv.style.display = 'block';
            } catch (error) {
                const resultDiv = document.getElementById('generateResult');
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `Network error: ${error.message}`;
                resultDiv.style.display = 'block';
            }
        });

        // Validate Token
        document.getElementById('validateForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const token = document.getElementById('validateToken').value.trim();
            if (!token) {
                alert('Please enter a token');
                return;
            }
            
            try {
                const response = await fetch('/api/v1/token/validate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ token: token })
                });
                
                const result = await response.json();
                const resultDiv = document.getElementById('validateResult');
                
                if (response.ok) {
                    if (result.valid) {
                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = `Token is valid!\n\n${JSON.stringify(result, null, 2)}`;
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.innerHTML = `Token is invalid!\n\nError: ${result.error}`;
                    }
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `Error: ${result.error}`;
                }
                
                resultDiv.style.display = 'block';
            } catch (error) {
                const resultDiv = document.getElementById('validateResult');
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `Network error: ${error.message}`;
                resultDiv.style.display = 'block';
            }
        });

        // WebSocket Connection Test
        document.getElementById('wsConnect').addEventListener('click', () => {
            const url = document.getElementById('wsUrl').value.trim();
            const token = document.getElementById('wsToken').value.trim();
            const tags = document.getElementById('wsTags').value.split(',').map(t => t.trim()).filter(t => t);
            
            if (!url) {
                alert('Please enter WebSocket URL');
                return;
            }
            
            try {
                ws = new WebSocket(url);
                const resultDiv = document.getElementById('wsResult');
                resultDiv.style.display = 'block';
                resultDiv.className = 'result';
                resultDiv.innerHTML = 'Connecting...';
                
                ws.onopen = () => {
                    resultDiv.innerHTML += '\nConnected to WebSocket server';
                    
                    // Send handshake message (simplified for demo)
                    const handshake = {
                        tags: tags,
                        clientInfo: 'JWT Demo Client',
                        jwtToken: token || undefined
                    };
                    
                    // Note: This is a simplified handshake for demo purposes
                    // In real implementation, you would use the proper protocol
                    ws.send(JSON.stringify(handshake));
                    
                    document.getElementById('wsConnect').disabled = true;
                    document.getElementById('wsDisconnect').disabled = false;
                };
                
                ws.onmessage = (event) => {
                    resultDiv.innerHTML += `\nReceived: ${event.data}`;
                };
                
                ws.onclose = (event) => {
                    resultDiv.innerHTML += `\nConnection closed (code: ${event.code})`;
                    document.getElementById('wsConnect').disabled = false;
                    document.getElementById('wsDisconnect').disabled = true;
                };
                
                ws.onerror = (error) => {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML += `\nWebSocket error: ${error}`;
                };
                
            } catch (error) {
                const resultDiv = document.getElementById('wsResult');
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `Connection error: ${error.message}`;
                resultDiv.style.display = 'block';
            }
        });

        document.getElementById('wsDisconnect').addEventListener('click', () => {
            if (ws) {
                ws.close();
                ws = null;
            }
        });
    </script>
</body>
</html>
