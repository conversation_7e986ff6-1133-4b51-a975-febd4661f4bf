package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"websocket-hub/internal/auth"
)

// JWTService JWT服务示例
type JWTService struct {
	jwtManager *auth.JWTManager
}

// TokenRequest 生成token请求
type TokenRequest struct {
	DeviceID string   `json:"device_id,omitempty"`
	Tags     []string `json:"tags,omitempty"`
	Expiry   int      `json:"expiry,omitempty"` // 过期时间（秒）
}

// TokenResponse 生成token响应
type TokenResponse struct {
	Token     string `json:"token"`
	ExpiresAt int64  `json:"expires_at"`
	DeviceID  string `json:"device_id,omitempty"`
}

// ValidateRequest 验证token请求
type ValidateRequest struct {
	Token string `json:"token"`
}

// ValidateResponse 验证token响应
type ValidateResponse struct {
	Valid     bool     `json:"valid"`
	DeviceID  string   `json:"device_id,omitempty"`
	Tags      []string `json:"tags,omitempty"`
	ExpiresAt int64    `json:"expires_at,omitempty"`
	Error     string   `json:"error,omitempty"`
}

func main() {
	// 创建JWT管理器
	jwtManager := auth.NewJWTManager(
		"jwt-secret-key-change-in-production", // 密钥
		"jwt-auth-example",                     // 签发者
		time.Hour,                              // 默认过期时间
		5*time.Minute,                          // 最大时钟偏差
		10*time.Minute,                         // 时间戳有效期
	)

	service := &JWTService{
		jwtManager: jwtManager,
	}

	// 创建Gin路由
	gin.SetMode(gin.ReleaseMode)
	r := gin.Default()

	// 添加CORS中间件
	r.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")
		
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}
		
		c.Next()
	})

	// API路由
	api := r.Group("/api/v1")
	{
		api.POST("/token/generate", service.generateToken)
		api.POST("/token/validate", service.validateToken)
		api.GET("/token/info/:token", service.getTokenInfo)
	}

	// 静态文件服务（用于演示页面）
	r.Static("/static", "./static")
	r.GET("/", func(c *gin.Context) {
		c.HTML(200, "index.html", gin.H{
			"title": "JWT Authentication Example",
		})
	})

	// 加载HTML模板
	r.LoadHTMLGlob("templates/*")

	fmt.Println("JWT Authentication Service started on :8081")
	fmt.Println("Visit http://localhost:8081 for the demo page")
	log.Fatal(r.Run(":8081"))
}

// generateToken 生成JWT token
func (s *JWTService) generateToken(c *gin.Context) {
	var req TokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request format"})
		return
	}

	// 设置默认值
	if req.Expiry == 0 {
		req.Expiry = 3600 // 1小时
	}

	// 生成token
	var token string
	var err error
	
	if req.Expiry > 0 {
		token, err = s.jwtManager.GenerateToken(req.DeviceID, req.Tags, time.Duration(req.Expiry)*time.Second)
	} else {
		token, err = s.jwtManager.GenerateToken(req.DeviceID, req.Tags)
	}

	if err != nil {
		c.JSON(500, gin.H{"error": fmt.Sprintf("Failed to generate token: %v", err)})
		return
	}

	// 获取过期时间
	claims, _ := s.jwtManager.ExtractClaimsWithoutValidation(token)
	
	response := TokenResponse{
		Token:    token,
		DeviceID: req.DeviceID,
	}
	
	if claims != nil && claims.ExpiresAt > 0 {
		response.ExpiresAt = claims.ExpiresAt
	}

	c.JSON(200, response)
}

// validateToken 验证JWT token
func (s *JWTService) validateToken(c *gin.Context) {
	var req ValidateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request format"})
		return
	}

	if req.Token == "" {
		c.JSON(400, gin.H{"error": "Token is required"})
		return
	}

	// 验证token
	claims, err := s.jwtManager.ValidateToken(req.Token)
	if err != nil {
		c.JSON(200, ValidateResponse{
			Valid: false,
			Error: err.Error(),
		})
		return
	}

	response := ValidateResponse{
		Valid:    true,
		DeviceID: claims.DeviceID,
		Tags:     claims.Tags,
	}
	
	if claims.ExpiresAt > 0 {
		response.ExpiresAt = claims.ExpiresAt
	}

	c.JSON(200, response)
}

// getTokenInfo 获取token信息（不验证）
func (s *JWTService) getTokenInfo(c *gin.Context) {
	token := c.Param("token")
	if token == "" {
		c.JSON(400, gin.H{"error": "Token is required"})
		return
	}

	// 提取声明（不验证）
	claims, err := s.jwtManager.ExtractClaimsWithoutValidation(token)
	if err != nil {
		c.JSON(400, gin.H{"error": fmt.Sprintf("Failed to parse token: %v", err)})
		return
	}

	// 检查是否过期
	isExpired := s.jwtManager.IsTokenExpired(token)
	
	// 获取剩余时间
	remainingTime, _ := s.jwtManager.GetTokenRemainingTime(token)

	response := gin.H{
		"device_id":      claims.DeviceID,
		"tags":           claims.Tags,
		"issuer":         claims.Issuer,
		"subject":        claims.Subject,
		"issued_at":      claims.IssuedAt,
		"expires_at":     claims.ExpiresAt,
		"timestamp":      claims.Timestamp,
		"is_expired":     isExpired,
		"remaining_time": remainingTime.Seconds(),
	}

	c.JSON(200, response)
}
