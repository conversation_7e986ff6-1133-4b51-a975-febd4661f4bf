# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2024-07-11

### Added
- Initial release of Qt WebSocket Client
- Binary protocol support with efficient message serialization
- WebSocket client with automatic reconnection
- React hooks for easy integration (`useQtWebSocket`, `useQtWebSocketEvent`, `useQtWebSocketState`)
- Support for multiple message types:
  - Unicast (point-to-point messaging)
  - Broadcast (message to all connected clients)
  - Tagcast (message to clients with specific tags)
- Connection state management
- Heartbeat mechanism for connection health monitoring
- Comprehensive TypeScript support
- Event-driven architecture
- Connection statistics tracking
- Configurable reconnection strategy
- Examples for React and Vanilla JavaScript
- Complete test suite
- Full documentation

### Features
- 🚀 **Binary Protocol**: Efficient binary message protocol, no JSON double parsing
- 🔐 **Secure**: Server-generated device IDs prevent malicious connections
- 🏷️ **Tag System**: Support for connection tags and tag-based message routing
- 📱 **React Ready**: Built-in React hooks for easy integration
- 🔄 **Auto Reconnect**: Automatic reconnection with configurable retry logic
- 💓 **Heartbeat**: Built-in heartbeat mechanism for connection health
- 📦 **TypeScript**: Full TypeScript support with comprehensive type definitions
- 🧪 **Well Tested**: Comprehensive test suite

### Technical Details
- Compatible with Qt WebSocket Hub server
- Supports WebSocket binary messages
- Automatic message marshaling/unmarshaling
- Connection state tracking
- Error handling and recovery
- Memory efficient implementation
- Cross-browser compatibility
- Node.js and browser support

### Documentation
- Complete API documentation
- React integration examples
- Vanilla JavaScript examples
- TypeScript type definitions
- Usage guides and best practices
