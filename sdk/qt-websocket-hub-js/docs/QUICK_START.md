# Quick Start Guide

Get up and running with Qt WebSocket Client in minutes!

## Installation

```bash
npm install qt-websocket-hub-js
```

## Basic Usage

### 1. Import the Client

```typescript
import { QtWebSocketClient } from 'qt-websocket-hub-js';
```

### 2. C<PERSON> and Connect

```typescript
const client = new QtWebSocketClient({
  url: 'ws://localhost:8080/api/v1/ws',
  tags: ['chat', 'user'],
  clientInfo: 'My App v1.0.0'
});

await client.connect();
```

### 3. Listen for Messages

```typescript
client.addEventListener('broadcast', (data) => {
  const message = new TextDecoder().decode(data);
  console.log('Received:', message);
});
```

### 4. Send Messages

```typescript
// Send to all connected clients
client.sendBroadcast('Hello everyone!');

// Send to specific device
client.sendUnicast('device-id-123', 'Hello specific device!');

// Send to devices with specific tags
client.sendTagcast(['admin', 'moderator'], 'Admin message');
```

## React Integration

### 1. Use the Hook

```tsx
import { useQtWebSocket } from 'qt-websocket-hub-js';

function ChatComponent() {
  const ws = useQtWebSocket({
    url: 'ws://localhost:8080/api/v1/ws',
    tags: ['chat'],
    enabled: true,
  });

  return (
    <div>
      <div>Status: {ws.state}</div>
      <div>Device ID: {ws.deviceId}</div>
      <button 
        onClick={() => ws.sendBroadcast('Hello!')}
        disabled={!ws.isConnected}
      >
        Send Message
      </button>
    </div>
  );
}
```

### 2. Listen for Events

```tsx
import { useQtWebSocketEvent } from 'qt-websocket-hub-js';

function MessageListener({ ws }) {
  useQtWebSocketEvent(ws, 'broadcast', (data) => {
    const message = new TextDecoder().decode(data);
    console.log('New message:', message);
  }, []);

  return <div>Listening for messages...</div>;
}
```

## Complete Example

```tsx
import React, { useState } from 'react';
import { useQtWebSocket, useQtWebSocketEvent } from 'qt-websocket-hub-js';

function App() {
  const [messages, setMessages] = useState<string[]>([]);
  const [input, setInput] = useState('');

  const ws = useQtWebSocket({
    url: 'ws://localhost:8080/api/v1/ws',
    tags: ['chat'],
  });

  useQtWebSocketEvent(ws, 'broadcast', (data) => {
    const message = new TextDecoder().decode(data);
    setMessages(prev => [...prev, message]);
  }, []);

  const sendMessage = () => {
    if (input.trim() && ws.isConnected) {
      ws.sendBroadcast(input);
      setInput('');
    }
  };

  return (
    <div>
      <h1>Chat App</h1>
      
      <div>Status: {ws.state}</div>
      
      <div style={{ height: '200px', overflow: 'auto', border: '1px solid #ccc' }}>
        {messages.map((msg, i) => (
          <div key={i}>{msg}</div>
        ))}
      </div>
      
      <div>
        <input
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
          disabled={!ws.isConnected}
        />
        <button onClick={sendMessage} disabled={!ws.isConnected}>
          Send
        </button>
      </div>
    </div>
  );
}

export default App;
```

## Configuration Options

```typescript
const config = {
  url: 'ws://localhost:8080/api/v1/ws',  // Required: WebSocket server URL
  tags: ['chat', 'user'],                // Optional: Connection tags
  deviceId: 'existing-device-id',        // Optional: Use existing device ID
  clientInfo: 'My App v1.0.0',          // Optional: Client information
  reconnect: true,                       // Optional: Enable auto-reconnect
  reconnectInterval: 3000,               // Optional: Reconnect interval (ms)
  maxReconnectAttempts: 10,              // Optional: Max reconnect attempts
  heartbeatInterval: 30000,              // Optional: Heartbeat interval (ms)
  connectionTimeout: 10000,              // Optional: Connection timeout (ms)
};
```

## Message Types

- **Broadcast**: Send to all connected clients
- **Unicast**: Send to a specific device
- **Tagcast**: Send to devices with specific tags

## Connection States

- `DISCONNECTED`: Not connected
- `CONNECTING`: Attempting to connect
- `CONNECTED`: Successfully connected
- `RECONNECTING`: Attempting to reconnect
- `ERROR`: Connection error

## Error Handling

```typescript
client.addEventListener('error', (error) => {
  console.error('WebSocket error:', error);
});

// In React
const ws = useQtWebSocket(config);
if (ws.error) {
  console.error('Connection error:', ws.error);
}
```

## Next Steps

- Check out the [full documentation](../README.md)
- Explore [React examples](../examples/react-chat/)
- Try the [Vanilla JS example](../examples/vanilla-js/)
- Learn about [advanced features](./ADVANCED.md)

## Need Help?

- 📚 [Full Documentation](../README.md)
- 🐛 [Report Issues](https://github.com/your-username/qt-websocket-client/issues)
- 💬 [Discussions](https://github.com/your-username/qt-websocket-client/discussions)
