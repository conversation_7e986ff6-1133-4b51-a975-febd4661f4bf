import { JWTUtils, J<PERSON>TTokenGenerator, jwt, JWTClaims } from '../jwt';

describe('JWTUtils', () => {
  const secretKey = 'test-secret-key';
  
  describe('generateToken and parseToken', () => {
    it('should generate and parse a valid token', () => {
      const claims: JWTClaims = {
        device_id: 'test-device-123',
        tags: ['user', 'mobile'],
        timestamp: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 3600
      };

      const token = JWTUtils.generateToken(claims, secretKey);
      expect(token).toBeTruthy();
      expect(token.split('.')).toHaveLength(3);

      const parsedClaims = JWTUtils.parseToken(token);
      expect(parsedClaims).toBeTruthy();
      expect(parsedClaims!.device_id).toBe(claims.device_id);
      expect(parsedClaims!.tags).toEqual(claims.tags);
      expect(parsedClaims!.timestamp).toBe(claims.timestamp);
      expect(parsedClaims!.exp).toBe(claims.exp);
    });

    it('should return null for invalid token format', () => {
      const invalidToken = 'invalid.token';
      const claims = JWTUtils.parseToken(invalidToken);
      expect(claims).toBeNull();
    });

    it('should return null for malformed token', () => {
      const malformedToken = 'header.invalid-json.signature';
      const claims = JWTUtils.parseToken(malformedToken);
      expect(claims).toBeNull();
    });
  });

  describe('isTokenExpired', () => {
    it('should return false for non-expired token', () => {
      const claims: JWTClaims = {
        timestamp: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 3600 // 1 hour from now
      };

      const token = JWTUtils.generateToken(claims, secretKey);
      const isExpired = JWTUtils.isTokenExpired(token);
      expect(isExpired).toBe(false);
    });

    it('should return true for expired token', () => {
      const claims: JWTClaims = {
        timestamp: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) - 3600 // 1 hour ago
      };

      const token = JWTUtils.generateToken(claims, secretKey);
      const isExpired = JWTUtils.isTokenExpired(token);
      expect(isExpired).toBe(true);
    });

    it('should return false for token without expiration', () => {
      const claims: JWTClaims = {
        timestamp: Math.floor(Date.now() / 1000)
        // no exp field
      };

      const token = JWTUtils.generateToken(claims, secretKey);
      const isExpired = JWTUtils.isTokenExpired(token);
      expect(isExpired).toBe(false);
    });
  });

  describe('getTokenRemainingTime', () => {
    it('should return correct remaining time', () => {
      const futureTime = Math.floor(Date.now() / 1000) + 1800; // 30 minutes from now
      const claims: JWTClaims = {
        timestamp: Math.floor(Date.now() / 1000),
        exp: futureTime
      };

      const token = JWTUtils.generateToken(claims, secretKey);
      const remainingTime = JWTUtils.getTokenRemainingTime(token);
      
      // Should be around 1800 seconds (30 minutes), allow some tolerance
      expect(remainingTime).toBeGreaterThan(1790);
      expect(remainingTime).toBeLessThan(1810);
    });

    it('should return 0 for expired token', () => {
      const pastTime = Math.floor(Date.now() / 1000) - 1800; // 30 minutes ago
      const claims: JWTClaims = {
        timestamp: Math.floor(Date.now() / 1000),
        exp: pastTime
      };

      const token = JWTUtils.generateToken(claims, secretKey);
      const remainingTime = JWTUtils.getTokenRemainingTime(token);
      expect(remainingTime).toBe(0);
    });

    it('should return 0 for token without expiration', () => {
      const claims: JWTClaims = {
        timestamp: Math.floor(Date.now() / 1000)
        // no exp field
      };

      const token = JWTUtils.generateToken(claims, secretKey);
      const remainingTime = JWTUtils.getTokenRemainingTime(token);
      expect(remainingTime).toBe(0);
    });
  });
});

describe('JWTTokenGenerator', () => {
  const secretKey = 'test-secret-key';
  const issuer = 'test-issuer';
  const defaultExpiry = 3600;

  let generator: JWTTokenGenerator;

  beforeEach(() => {
    generator = new JWTTokenGenerator(secretKey, issuer, defaultExpiry);
  });

  describe('generateDeviceToken', () => {
    it('should generate token with device information', () => {
      const deviceId = 'test-device-123';
      const tags = ['user', 'mobile'];
      
      const token = generator.generateDeviceToken(deviceId, tags);
      expect(token).toBeTruthy();

      const claims = JWTUtils.parseToken(token);
      expect(claims).toBeTruthy();
      expect(claims!.iss).toBe(issuer);
      expect(claims!.sub).toBe(deviceId);
      expect(claims!.device_id).toBe(deviceId);
      expect(claims!.tags).toEqual(tags);
      expect(claims!.exp).toBeGreaterThan(Math.floor(Date.now() / 1000));
    });

    it('should generate token without device ID', () => {
      const tags = ['user', 'mobile'];
      
      const token = generator.generateDeviceToken(undefined, tags);
      expect(token).toBeTruthy();

      const claims = JWTUtils.parseToken(token);
      expect(claims).toBeTruthy();
      expect(claims!.iss).toBe(issuer);
      expect(claims!.tags).toEqual(tags);
      expect(claims!.device_id).toBeUndefined();
      expect(claims!.sub).toBeUndefined();
    });

    it('should use custom expiry time', () => {
      const deviceId = 'test-device-123';
      const tags = ['user'];
      const customExpiry = 1800; // 30 minutes
      
      const token = generator.generateDeviceToken(deviceId, tags, customExpiry);
      const claims = JWTUtils.parseToken(token);
      
      expect(claims).toBeTruthy();
      const expectedExp = Math.floor(Date.now() / 1000) + customExpiry;
      
      // Allow some tolerance for timing
      expect(claims!.exp).toBeGreaterThan(expectedExp - 5);
      expect(claims!.exp).toBeLessThan(expectedExp + 5);
    });
  });

  describe('generateTemporaryToken', () => {
    it('should generate temporary token with default duration', () => {
      const token = generator.generateTemporaryToken();
      const claims = JWTUtils.parseToken(token);
      
      expect(claims).toBeTruthy();
      expect(claims!.iss).toBe(issuer);
      
      const expectedExp = Math.floor(Date.now() / 1000) + 300; // 5 minutes default
      expect(claims!.exp).toBeGreaterThan(expectedExp - 5);
      expect(claims!.exp).toBeLessThan(expectedExp + 5);
    });

    it('should generate temporary token with custom duration', () => {
      const duration = 600; // 10 minutes
      const token = generator.generateTemporaryToken(duration);
      const claims = JWTUtils.parseToken(token);
      
      expect(claims).toBeTruthy();
      const expectedExp = Math.floor(Date.now() / 1000) + duration;
      expect(claims!.exp).toBeGreaterThan(expectedExp - 5);
      expect(claims!.exp).toBeLessThan(expectedExp + 5);
    });
  });

  describe('updateSecretKey', () => {
    it('should update secret key', () => {
      const newSecretKey = 'new-secret-key';
      const deviceId = 'test-device';
      
      // Generate token with old key
      const oldToken = generator.generateDeviceToken(deviceId);
      
      // Update key
      generator.updateSecretKey(newSecretKey);
      
      // Generate token with new key
      const newToken = generator.generateDeviceToken(deviceId);
      
      // Tokens should be different
      expect(oldToken).not.toBe(newToken);
    });
  });

  describe('updateDefaultExpiry', () => {
    it('should update default expiry time', () => {
      const newExpiry = 7200; // 2 hours
      generator.updateDefaultExpiry(newExpiry);
      
      const token = generator.generateDeviceToken('test-device');
      const claims = JWTUtils.parseToken(token);
      
      expect(claims).toBeTruthy();
      const expectedExp = Math.floor(Date.now() / 1000) + newExpiry;
      expect(claims!.exp).toBeGreaterThan(expectedExp - 5);
      expect(claims!.exp).toBeLessThan(expectedExp + 5);
    });
  });
});

describe('jwt utility functions', () => {
  const secretKey = 'test-secret-key';

  describe('generateDeviceToken', () => {
    it('should generate device token with all parameters', () => {
      const deviceId = 'test-device-123';
      const tags = ['user', 'mobile'];
      const expiry = 1800;
      
      const token = jwt.generateDeviceToken(secretKey, deviceId, tags, expiry);
      expect(token).toBeTruthy();
      
      const claims = jwt.parseToken(token);
      expect(claims).toBeTruthy();
      expect(claims!.device_id).toBe(deviceId);
      expect(claims!.tags).toEqual(tags);
    });

    it('should generate device token with minimal parameters', () => {
      const token = jwt.generateDeviceToken(secretKey);
      expect(token).toBeTruthy();
      
      const claims = jwt.parseToken(token);
      expect(claims).toBeTruthy();
    });
  });

  describe('parseToken', () => {
    it('should parse valid token', () => {
      const token = jwt.generateDeviceToken(secretKey, 'test-device');
      const claims = jwt.parseToken(token);
      
      expect(claims).toBeTruthy();
      expect(claims!.device_id).toBe('test-device');
    });
  });

  describe('isExpired', () => {
    it('should check if token is expired', () => {
      const validToken = jwt.generateDeviceToken(secretKey, 'test-device', [], 3600);
      const expiredToken = jwt.generateDeviceToken(secretKey, 'test-device', [], -3600);
      
      expect(jwt.isExpired(validToken)).toBe(false);
      expect(jwt.isExpired(expiredToken)).toBe(true);
    });
  });

  describe('getRemainingTime', () => {
    it('should get remaining time', () => {
      const token = jwt.generateDeviceToken(secretKey, 'test-device', [], 1800);
      const remainingTime = jwt.getRemainingTime(token);
      
      expect(remainingTime).toBeGreaterThan(1790);
      expect(remainingTime).toBeLessThan(1810);
    });
  });
});
