import { MessageProtocol, stringToUint8Array, uint8ArrayToString } from '../protocol';
import { MessageType } from '../types';

describe('MessageProtocol', () => {
  describe('createMessage', () => {
    it('should create a valid message', () => {
      const payload = stringToUint8Array('Hello, World!');
      const message = MessageProtocol.createMessage(MessageType.UNICAST, payload);

      expect(message.header.magic).toBe(0x5757);
      expect(message.header.version).toBe(0x01);
      expect(message.header.type).toBe(MessageType.UNICAST);
      expect(message.header.payloadLen).toBe(payload.length);
      expect(message.payload).toEqual(payload);
      expect(message.header.checksum).toBeGreaterThan(0);
    });
  });

  describe('marshal and unmarshal', () => {
    it('should marshal and unmarshal a message correctly', () => {
      const originalPayload = stringToUint8Array('Test message');
      const originalMessage = MessageProtocol.createMessage(MessageType.BROADCAST, originalPayload);

      // Marshal
      const data = MessageProtocol.marshal(originalMessage);
      expect(data).toBeInstanceOf(Uint8Array);
      expect(data.length).toBe(16 + originalPayload.length);

      // Unmarshal
      const unmarshaledMessage = MessageProtocol.unmarshal(data);
      expect(unmarshaledMessage.header.magic).toBe(originalMessage.header.magic);
      expect(unmarshaledMessage.header.version).toBe(originalMessage.header.version);
      expect(unmarshaledMessage.header.type).toBe(originalMessage.header.type);
      expect(unmarshaledMessage.header.payloadLen).toBe(originalMessage.header.payloadLen);
      expect(unmarshaledMessage.payload).toEqual(originalMessage.payload);
      expect(uint8ArrayToString(unmarshaledMessage.payload)).toBe('Test message');
    });

    it('should handle empty payload', () => {
      const emptyPayload = new Uint8Array(0);
      const message = MessageProtocol.createMessage(MessageType.HEARTBEAT, emptyPayload);

      const data = MessageProtocol.marshal(message);
      const unmarshaledMessage = MessageProtocol.unmarshal(data);

      expect(unmarshaledMessage.header.payloadLen).toBe(0);
      expect(unmarshaledMessage.payload.length).toBe(0);
    });

    it('should throw error for invalid data', () => {
      const invalidData = new Uint8Array(10); // Too short for header
      expect(() => MessageProtocol.unmarshal(invalidData)).toThrow('Data too short for message header');
    });

    it('should throw error for invalid magic', () => {
      const payload = stringToUint8Array('test');
      const message = MessageProtocol.createMessage(MessageType.UNICAST, payload);
      const data = MessageProtocol.marshal(message);
      
      // Corrupt magic number
      data[0] = 0x00;
      data[1] = 0x00;
      
      expect(() => MessageProtocol.unmarshal(data)).toThrow('Invalid message magic');
    });
  });

  describe('calculateChecksum', () => {
    it('should calculate consistent checksum', () => {
      const payload = stringToUint8Array('Checksum test');
      const message = MessageProtocol.createMessage(MessageType.TAGCAST, payload);
      
      const checksum1 = MessageProtocol.calculateChecksum(message);
      const checksum2 = MessageProtocol.calculateChecksum(message);
      
      expect(checksum1).toBe(checksum2);
      expect(checksum1).toBe(message.header.checksum);
    });

    it('should produce different checksums for different payloads', () => {
      const payload1 = stringToUint8Array('Message 1');
      const payload2 = stringToUint8Array('Message 2');
      
      const message1 = MessageProtocol.createMessage(MessageType.UNICAST, payload1);
      const message2 = MessageProtocol.createMessage(MessageType.UNICAST, payload2);
      
      expect(message1.header.checksum).not.toBe(message2.header.checksum);
    });
  });

  describe('isValid', () => {
    it('should validate correct message', () => {
      const payload = stringToUint8Array('Valid message');
      const message = MessageProtocol.createMessage(MessageType.BROADCAST, payload);
      
      expect(MessageProtocol.isValid(message)).toBe(true);
    });

    it('should invalidate corrupted message', () => {
      const payload = stringToUint8Array('Valid message');
      const message = MessageProtocol.createMessage(MessageType.BROADCAST, payload);
      
      // Corrupt checksum
      message.header.checksum = 0;
      
      expect(MessageProtocol.isValid(message)).toBe(false);
    });
  });

  describe('flags', () => {
    it('should set and check flags correctly', () => {
      const payload = stringToUint8Array('Flag test');
      const message = MessageProtocol.createMessage(MessageType.UNICAST, payload);
      
      expect(MessageProtocol.hasFlag(message, 0x01)).toBe(false);
      
      MessageProtocol.setFlag(message, 0x01);
      expect(MessageProtocol.hasFlag(message, 0x01)).toBe(true);
      expect(MessageProtocol.hasFlag(message, 0x02)).toBe(false);
      
      MessageProtocol.setFlag(message, 0x02);
      expect(MessageProtocol.hasFlag(message, 0x01)).toBe(true);
      expect(MessageProtocol.hasFlag(message, 0x02)).toBe(true);
    });
  });
});

describe('Utility functions', () => {
  describe('stringToUint8Array and uint8ArrayToString', () => {
    it('should convert strings correctly', () => {
      const testStrings = [
        'Hello, World!',
        '你好，世界！',
        'Emoji test: 🚀🎉',
        '',
        'Special chars: !@#$%^&*()',
      ];

      testStrings.forEach(str => {
        const uint8Array = stringToUint8Array(str);
        const convertedBack = uint8ArrayToString(uint8Array);
        expect(convertedBack).toBe(str);
      });
    });
  });
});
