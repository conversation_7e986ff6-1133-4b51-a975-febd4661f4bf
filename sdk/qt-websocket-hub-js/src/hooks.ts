import { useEffect, useRef, useState, useCallback } from 'react';
import { QtWebSocketClient } from './client';
import {
  UseWebSocketConfig,
  UseWebSocketReturn,
  ConnectionState,
  WebSocketEvents,
} from './types';

/**
 * React Hook for Qt WebSocket Client
 */
export function useQtWebSocket(config: UseWebSocketConfig): UseWebSocketReturn {
  const [state, setState] = useState<ConnectionState>(ConnectionState.DISCONNECTED);
  const [deviceId, setDeviceId] = useState<string | null>(null);
  const [error, setError] = useState<Error | null>(null);
  
  const clientRef = useRef<QtWebSocketClient | null>(null);
  const eventListenersRef = useRef<Map<keyof WebSocketEvents, Set<any>>>(new Map());

  // 初始化客户端
  useEffect(() => {
    if (!config.enabled) {
      return;
    }

    const client = new QtWebSocketClient(config);
    clientRef.current = client;

    // 设置事件监听器
    client.addEventListener('connect', (deviceId: string) => {
      setDeviceId(deviceId);
      setError(null);
    });

    client.addEventListener('disconnect', (reason: string) => {
      setDeviceId(null);
      console.log('Disconnected:', reason);
    });

    client.addEventListener('error', (error: Error) => {
      setError(error);
    });

    client.addEventListener('stateChange', (newState: ConnectionState) => {
      setState(newState);
    });

    // 自动连接
    if (config.enabled !== false) {
      client.connect().catch(err => {
        console.error('Failed to connect:', err);
        setError(err);
      });
    }

    return () => {
      client.disconnect();
      clientRef.current = null;
    };
  }, [config.url, config.enabled]);

  // 连接函数
  const connect = useCallback(() => {
    if (clientRef.current) {
      clientRef.current.connect().catch(err => {
        console.error('Failed to connect:', err);
        setError(err);
      });
    }
  }, []);

  // 断开连接函数
  const disconnect = useCallback(() => {
    if (clientRef.current) {
      clientRef.current.disconnect();
    }
  }, []);

  // 发送单播消息
  const sendUnicast = useCallback((targetDeviceId: string, data: Uint8Array | string) => {
    if (clientRef.current) {
      try {
        clientRef.current.sendUnicast(targetDeviceId, data);
      } catch (error) {
        console.error('Failed to send unicast message:', error);
        setError(error as Error);
      }
    }
  }, []);

  // 发送广播消息
  const sendBroadcast = useCallback((data: Uint8Array | string) => {
    if (clientRef.current) {
      try {
        clientRef.current.sendBroadcast(data);
      } catch (error) {
        console.error('Failed to send broadcast message:', error);
        setError(error as Error);
      }
    }
  }, []);

  // 发送标签组播消息
  const sendTagcast = useCallback((tags: string[], data: Uint8Array | string) => {
    if (clientRef.current) {
      try {
        clientRef.current.sendTagcast(tags, data);
      } catch (error) {
        console.error('Failed to send tagcast message:', error);
        setError(error as Error);
      }
    }
  }, []);

  // 添加事件监听器
  const addEventListener = useCallback(<K extends keyof WebSocketEvents>(
    event: K,
    listener: WebSocketEvents[K]
  ) => {
    if (clientRef.current) {
      clientRef.current.addEventListener(event, listener);
      
      // 跟踪监听器以便清理
      if (!eventListenersRef.current.has(event)) {
        eventListenersRef.current.set(event, new Set());
      }
      eventListenersRef.current.get(event)!.add(listener);
    }
  }, []);

  // 移除事件监听器
  const removeEventListener = useCallback(<K extends keyof WebSocketEvents>(
    event: K,
    listener: WebSocketEvents[K]
  ) => {
    if (clientRef.current) {
      clientRef.current.removeEventListener(event, listener);
      
      // 从跟踪中移除
      const listeners = eventListenersRef.current.get(event);
      if (listeners) {
        listeners.delete(listener);
        if (listeners.size === 0) {
          eventListenersRef.current.delete(event);
        }
      }
    }
  }, []);

  // 清理事件监听器
  useEffect(() => {
    return () => {
      if (clientRef.current) {
        eventListenersRef.current.forEach((listeners, event) => {
          listeners.forEach(listener => {
            clientRef.current!.removeEventListener(event, listener);
          });
        });
        eventListenersRef.current.clear();
      }
    };
  }, []);

  return {
    state,
    deviceId,
    isConnected: state === ConnectionState.CONNECTED,
    isConnecting: state === ConnectionState.CONNECTING,
    error,
    connect,
    disconnect,
    sendUnicast,
    sendBroadcast,
    sendTagcast,
    addEventListener,
    removeEventListener,
  };
}

/**
 * Hook for listening to specific WebSocket events
 */
export function useQtWebSocketEvent<K extends keyof WebSocketEvents>(
  client: UseWebSocketReturn,
  event: K,
  handler: WebSocketEvents[K],
  deps: React.DependencyList = []
): void {
  useEffect(() => {
    client.addEventListener(event, handler);
    return () => {
      client.removeEventListener(event, handler);
    };
  }, [client, event, ...deps]);
}

/**
 * Hook for WebSocket connection state
 */
export function useQtWebSocketState(client: UseWebSocketReturn) {
  return {
    state: client.state,
    isConnected: client.isConnected,
    isConnecting: client.isConnecting,
    isDisconnected: client.state === ConnectionState.DISCONNECTED,
    isReconnecting: client.state === ConnectionState.RECONNECTING,
    hasError: client.state === ConnectionState.ERROR,
    deviceId: client.deviceId,
    error: client.error,
  };
}
