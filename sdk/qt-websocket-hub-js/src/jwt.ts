/**
 * JWT工具类 - 用于生成和解析JWT token
 * 注意：这是一个简化的实现，主要用于客户端生成token
 * 在生产环境中，token通常由服务端生成并提供给客户端
 */

// JWT声明接口
export interface JWTClaims {
  // 标准声明
  iss?: string; // 签发者
  sub?: string; // 主题
  aud?: string; // 受众
  exp?: number; // 过期时间
  nbf?: number; // 生效时间
  iat?: number; // 签发时间
  jti?: string; // JWT ID

  // 自定义声明
  device_id?: string; // 设备ID
  tags?: string[]; // 设备标签
  timestamp: number; // 当前时间戳（用于验证）
}

// JWT头部接口
export interface JWTHeader {
  alg: string; // 算法
  typ: string; // 类型
}

/**
 * JWT工具类
 */
export class JWTUtils {
  /**
   * 生成JWT token（仅用于测试，生产环境应由服务端生成）
   * @param claims JWT声明
   * @param secretKey 密钥
   * @returns JWT token字符串
   */
  static generateToken(claims: JWTClaims, secretKey: string): string {
    const header: JWTHeader = {
      alg: 'HS256',
      typ: 'JWT'
    };

    // 设置默认值
    const now = Math.floor(Date.now() / 1000);
    const fullClaims: JWTClaims = {
      iat: now,
      timestamp: now,
      ...claims
    };

    // 编码头部和载荷
    const headerEncoded = this.base64UrlEncode(JSON.stringify(header));
    const payloadEncoded = this.base64UrlEncode(JSON.stringify(fullClaims));

    // 创建签名（简化实现，实际应使用HMAC-SHA256）
    const signature = this.createSignature(headerEncoded + '.' + payloadEncoded, secretKey);
    const signatureEncoded = this.base64UrlEncode(signature);

    return `${headerEncoded}.${payloadEncoded}.${signatureEncoded}`;
  }

  /**
   * 解析JWT token（不验证签名）
   * @param token JWT token字符串
   * @returns JWT声明
   */
  static parseToken(token: string): JWTClaims | null {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) {
        return null;
      }

      const payloadJson = this.base64UrlDecode(parts[1]);
      return JSON.parse(payloadJson);
    } catch (error) {
      console.error('Failed to parse JWT token:', error);
      return null;
    }
  }

  /**
   * 检查token是否过期
   * @param token JWT token字符串
   * @returns 是否过期
   */
  static isTokenExpired(token: string): boolean {
    const claims = this.parseToken(token);
    if (!claims || !claims.exp) {
      return false; // 没有过期时间设置
    }

    const now = Math.floor(Date.now() / 1000);
    return now > claims.exp;
  }

  /**
   * 获取token剩余有效时间（秒）
   * @param token JWT token字符串
   * @returns 剩余时间（秒），如果已过期返回0
   */
  static getTokenRemainingTime(token: string): number {
    const claims = this.parseToken(token);
    if (!claims || !claims.exp) {
      return 0;
    }

    const now = Math.floor(Date.now() / 1000);
    const remaining = claims.exp - now;
    return remaining > 0 ? remaining : 0;
  }

  /**
   * Base64 URL编码
   * @param str 要编码的字符串
   * @returns 编码后的字符串
   */
  private static base64UrlEncode(str: string): string {
    const base64 = btoa(unescape(encodeURIComponent(str)));
    return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
  }

  /**
   * Base64 URL解码
   * @param str 要解码的字符串
   * @returns 解码后的字符串
   */
  private static base64UrlDecode(str: string): string {
    // 补充padding
    str += '='.repeat((4 - str.length % 4) % 4);
    // 替换URL安全字符
    const base64 = str.replace(/-/g, '+').replace(/_/g, '/');
    return decodeURIComponent(escape(atob(base64)));
  }

  /**
   * 创建简化签名（仅用于演示，实际应使用HMAC-SHA256）
   * @param data 要签名的数据
   * @param secretKey 密钥
   * @returns 签名字符串
   */
  private static createSignature(data: string, secretKey: string): string {
    // 这是一个简化的签名实现，仅用于演示
    // 在实际应用中应该使用crypto库的HMAC-SHA256
    let hash = 0;
    const combined = data + secretKey;
    
    for (let i = 0; i < combined.length; i++) {
      const char = combined.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    
    return Math.abs(hash).toString(16);
  }
}

/**
 * JWT Token生成器类
 * 用于方便地生成包含设备信息的JWT token
 */
export class JWTTokenGenerator {
  private secretKey: string;
  private issuer: string;
  private defaultExpiry: number; // 默认过期时间（秒）

  constructor(secretKey: string, issuer: string = 'websocket-hub-client', defaultExpiry: number = 3600) {
    this.secretKey = secretKey;
    this.issuer = issuer;
    this.defaultExpiry = defaultExpiry;
  }

  /**
   * 生成设备认证token
   * @param deviceId 设备ID（可选）
   * @param tags 设备标签
   * @param customExpiry 自定义过期时间（秒）
   * @returns JWT token字符串
   */
  generateDeviceToken(deviceId?: string, tags: string[] = [], customExpiry?: number): string {
    const now = Math.floor(Date.now() / 1000);
    const expiry = customExpiry || this.defaultExpiry;

    const claims: JWTClaims = {
      iss: this.issuer,
      sub: deviceId,
      iat: now,
      exp: now + expiry,
      device_id: deviceId,
      tags: tags,
      timestamp: now
    };

    return JWTUtils.generateToken(claims, this.secretKey);
  }

  /**
   * 生成临时访问token
   * @param duration 有效期（秒）
   * @returns JWT token字符串
   */
  generateTemporaryToken(duration: number = 300): string {
    const now = Math.floor(Date.now() / 1000);

    const claims: JWTClaims = {
      iss: this.issuer,
      iat: now,
      exp: now + duration,
      timestamp: now
    };

    return JWTUtils.generateToken(claims, this.secretKey);
  }

  /**
   * 更新密钥
   * @param newSecretKey 新密钥
   */
  updateSecretKey(newSecretKey: string): void {
    this.secretKey = newSecretKey;
  }

  /**
   * 更新默认过期时间
   * @param newDefaultExpiry 新的默认过期时间（秒）
   */
  updateDefaultExpiry(newDefaultExpiry: number): void {
    this.defaultExpiry = newDefaultExpiry;
  }
}

// 导出工具函数
export const jwt = {
  /**
   * 快速生成设备token
   */
  generateDeviceToken: (secretKey: string, deviceId?: string, tags: string[] = [], expiry: number = 3600) => {
    const generator = new JWTTokenGenerator(secretKey);
    return generator.generateDeviceToken(deviceId, tags, expiry);
  },

  /**
   * 快速解析token
   */
  parseToken: JWTUtils.parseToken,

  /**
   * 快速检查token是否过期
   */
  isExpired: JWTUtils.isTokenExpired,

  /**
   * 快速获取token剩余时间
   */
  getRemainingTime: JWTUtils.getTokenRemainingTime
};
