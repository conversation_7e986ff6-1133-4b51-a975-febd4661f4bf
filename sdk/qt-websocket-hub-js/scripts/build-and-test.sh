#!/bin/bash

# Qt WebSocket Client 构建和测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数定义
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否在正确的目录
if [ ! -f "package.json" ]; then
    log_error "package.json not found. Please run this script from the package root directory."
    exit 1
fi

log_info "Qt WebSocket Client - Build and Test"
log_info "======================================"

# 检查Node.js版本
NODE_VERSION=$(node --version)
log_info "Node.js version: $NODE_VERSION"

# 检查npm版本
NPM_VERSION=$(npm --version)
log_info "npm version: $NPM_VERSION"

# 清理之前的构建
log_step "Cleaning previous build..."
rm -rf dist/ coverage/ node_modules/.cache/

# 安装依赖
log_step "Installing dependencies..."
npm ci

# 运行linting
log_step "Running ESLint..."
npm run lint

# 运行测试
log_step "Running tests..."
npm test

# 构建项目
log_step "Building project..."
npm run build

# 检查构建输出
log_step "Verifying build output..."
if [ ! -d "dist" ]; then
    log_error "dist directory not found"
    exit 1
fi

REQUIRED_FILES=(
    "dist/index.js"
    "dist/index.esm.js"
    "dist/index.d.ts"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        log_error "Required file not found: $file"
        exit 1
    fi
    log_info "✓ Found: $file"
done

# 检查文件大小
log_step "Checking bundle sizes..."
for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        size=$(du -h "$file" | cut -f1)
        log_info "  $file: $size"
    fi
done

# 验证TypeScript类型
log_step "Verifying TypeScript types..."
if [ -f "dist/index.d.ts" ]; then
    log_info "✓ TypeScript declarations generated"
else
    log_error "TypeScript declarations not found"
    exit 1
fi

# 检查package.json字段
log_step "Verifying package.json..."
PACKAGE_NAME=$(node -p "require('./package.json').name")
PACKAGE_VERSION=$(node -p "require('./package.json').version")
MAIN_FIELD=$(node -p "require('./package.json').main")
MODULE_FIELD=$(node -p "require('./package.json').module")
TYPES_FIELD=$(node -p "require('./package.json').types")

log_info "Package: $PACKAGE_NAME@$PACKAGE_VERSION"
log_info "Main: $MAIN_FIELD"
log_info "Module: $MODULE_FIELD"
log_info "Types: $TYPES_FIELD"

# 验证入口文件
if [ ! -f "$MAIN_FIELD" ]; then
    log_error "Main entry file not found: $MAIN_FIELD"
    exit 1
fi

if [ ! -f "$MODULE_FIELD" ]; then
    log_error "Module entry file not found: $MODULE_FIELD"
    exit 1
fi

if [ ! -f "$TYPES_FIELD" ]; then
    log_error "Types entry file not found: $TYPES_FIELD"
    exit 1
fi

# 模拟npm pack
log_step "Testing npm pack..."
npm pack --dry-run > /dev/null

# 检查导出
log_step "Verifying exports..."
node -e "
const pkg = require('./dist/index.js');
const requiredExports = ['QtWebSocketClient', 'useQtWebSocket', 'MessageType', 'ConnectionState'];
const missing = requiredExports.filter(exp => !pkg[exp]);
if (missing.length > 0) {
  console.error('Missing exports:', missing);
  process.exit(1);
}
console.log('✓ All required exports found');
"

# 运行基本功能测试
log_step "Running basic functionality test..."
node -e "
const { QtWebSocketClient, MessageType, ConnectionState } = require('./dist/index.js');

// 测试类实例化
try {
  const client = new QtWebSocketClient({
    url: 'ws://localhost:8080/api/v1/ws',
    tags: ['test']
  });
  console.log('✓ QtWebSocketClient instantiation successful');
} catch (error) {
  console.error('✗ QtWebSocketClient instantiation failed:', error.message);
  process.exit(1);
}

// 测试枚举
if (typeof MessageType.BROADCAST !== 'number') {
  console.error('✗ MessageType enum not working');
  process.exit(1);
}
console.log('✓ MessageType enum working');

if (typeof ConnectionState.CONNECTED !== 'string') {
  console.error('✗ ConnectionState enum not working');
  process.exit(1);
}
console.log('✓ ConnectionState enum working');

console.log('✓ Basic functionality test passed');
"

log_info ""
log_info "🎉 All checks passed!"
log_info "Package is ready for publishing."
log_info ""
log_info "Next steps:"
echo "  1. Review the build output in dist/"
echo "  2. Test with a real WebSocket server"
echo "  3. Run './scripts/publish.sh' to publish to npm"
echo ""
log_info "Build summary:"
echo "  📦 Package: $PACKAGE_NAME@$PACKAGE_VERSION"
echo "  📁 Output: dist/"
echo "  🧪 Tests: Passed"
echo "  📝 Types: Generated"
echo "  ✅ Ready to publish"
