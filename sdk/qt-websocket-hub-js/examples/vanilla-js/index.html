<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Qt WebSocket Client - Vanilla JS Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .status {
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 4px;
            border: 1px solid;
        }
        
        .status.connected {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .status.disconnected {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        .status.connecting {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        
        .controls label {
            display: block;
            margin-bottom: 10px;
        }
        
        .controls input, .controls select, .controls textarea {
            margin-left: 10px;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        
        .messages {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin-bottom: 20px;
            background-color: #fafafa;
        }
        
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
            word-wrap: break-word;
        }
        
        .message.sent {
            background-color: #007bff;
            color: white;
            text-align: right;
        }
        
        .message.received {
            background-color: #28a745;
            color: white;
        }
        
        .message.system {
            background-color: #6c757d;
            color: white;
            font-style: italic;
        }
        
        .message-time {
            font-size: 0.8em;
            opacity: 0.8;
            margin-top: 5px;
        }
        
        .input-area {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        .input-area textarea {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
            min-height: 60px;
        }
        
        .input-area button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .input-area button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        .button-group button {
            padding: 8px 16px;
            border: 1px solid #007bff;
            background-color: white;
            color: #007bff;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .button-group button:hover {
            background-color: #007bff;
            color: white;
        }
        
        .button-group button:disabled {
            border-color: #6c757d;
            color: #6c757d;
            cursor: not-allowed;
        }
        
        .button-group button:disabled:hover {
            background-color: white;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Qt WebSocket Client - Vanilla JS Example</h1>
        
        <!-- Connection Status -->
        <div id="status" class="status disconnected">
            <div><strong>Status:</strong> <span id="connection-state">Disconnected</span></div>
            <div><strong>Device ID:</strong> <span id="device-id">Not assigned</span></div>
            <div id="error-message" style="display: none;"><strong>Error:</strong> <span id="error-text"></span></div>
        </div>
        
        <!-- Connection Controls -->
        <div class="controls">
            <label>
                <strong>Server URL:</strong>
                <input type="text" id="server-url" value="ws://localhost:8080/api/v1/ws" style="width: 300px;">
            </label>
            <label>
                <strong>Tags (comma-separated):</strong>
                <input type="text" id="tags" value="chat,demo" style="width: 200px;">
            </label>
            <div class="button-group">
                <button id="connect-btn">Connect</button>
                <button id="disconnect-btn" disabled>Disconnect</button>
                <button id="clear-messages-btn">Clear Messages</button>
            </div>
        </div>
        
        <!-- Message Type Selection -->
        <div class="controls">
            <label>
                <strong>Message Type:</strong>
                <select id="message-type">
                    <option value="broadcast">Broadcast</option>
                    <option value="unicast">Unicast</option>
                    <option value="tagcast">Tagcast</option>
                </select>
            </label>
            <label id="target-device-label" style="display: none;">
                <strong>Target Device ID:</strong>
                <input type="text" id="target-device" placeholder="Enter target device ID">
            </label>
            <label id="target-tags-label" style="display: none;">
                <strong>Target Tags (comma-separated):</strong>
                <input type="text" id="target-tags" placeholder="Enter target tags">
            </label>
        </div>
        
        <!-- Messages -->
        <div id="messages" class="messages"></div>
        
        <!-- Message Input -->
        <div class="input-area">
            <textarea id="message-input" placeholder="Type your message..." disabled></textarea>
            <button id="send-btn" disabled>Send</button>
        </div>
    </div>

    <!-- Include the Qt WebSocket Client library -->
    <script src="../../dist/index.js"></script>
    
    <script>
        // Global variables
        let client = null;
        let isConnected = false;
        
        // DOM elements
        const statusEl = document.getElementById('status');
        const connectionStateEl = document.getElementById('connection-state');
        const deviceIdEl = document.getElementById('device-id');
        const errorMessageEl = document.getElementById('error-message');
        const errorTextEl = document.getElementById('error-text');
        const serverUrlEl = document.getElementById('server-url');
        const tagsEl = document.getElementById('tags');
        const connectBtn = document.getElementById('connect-btn');
        const disconnectBtn = document.getElementById('disconnect-btn');
        const clearMessagesBtn = document.getElementById('clear-messages-btn');
        const messageTypeEl = document.getElementById('message-type');
        const targetDeviceLabel = document.getElementById('target-device-label');
        const targetDeviceEl = document.getElementById('target-device');
        const targetTagsLabel = document.getElementById('target-tags-label');
        const targetTagsEl = document.getElementById('target-tags');
        const messagesEl = document.getElementById('messages');
        const messageInputEl = document.getElementById('message-input');
        const sendBtn = document.getElementById('send-btn');
        
        // Update UI based on connection state
        function updateUI(state) {
            connectionStateEl.textContent = state;
            
            switch (state) {
                case 'connected':
                    statusEl.className = 'status connected';
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = false;
                    messageInputEl.disabled = false;
                    sendBtn.disabled = false;
                    isConnected = true;
                    break;
                case 'connecting':
                    statusEl.className = 'status connecting';
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = false;
                    messageInputEl.disabled = true;
                    sendBtn.disabled = true;
                    isConnected = false;
                    break;
                default:
                    statusEl.className = 'status disconnected';
                    connectBtn.disabled = false;
                    disconnectBtn.disabled = true;
                    messageInputEl.disabled = true;
                    sendBtn.disabled = true;
                    isConnected = false;
                    deviceIdEl.textContent = 'Not assigned';
                    break;
            }
        }
        
        // Add message to chat
        function addMessage(content, type = 'received', timestamp = new Date()) {
            const messageEl = document.createElement('div');
            messageEl.className = `message ${type}`;
            
            const contentEl = document.createElement('div');
            contentEl.textContent = content;
            messageEl.appendChild(contentEl);
            
            const timeEl = document.createElement('div');
            timeEl.className = 'message-time';
            timeEl.textContent = timestamp.toLocaleTimeString();
            messageEl.appendChild(timeEl);
            
            messagesEl.appendChild(messageEl);
            messagesEl.scrollTop = messagesEl.scrollHeight;
        }
        
        // Show error
        function showError(error) {
            errorTextEl.textContent = error.message || error;
            errorMessageEl.style.display = 'block';
        }
        
        // Hide error
        function hideError() {
            errorMessageEl.style.display = 'none';
        }
        
        // Connect to WebSocket
        function connect() {
            const url = serverUrlEl.value.trim();
            const tags = tagsEl.value.split(',').map(tag => tag.trim()).filter(tag => tag);
            
            if (!url) {
                alert('Please enter server URL');
                return;
            }
            
            try {
                client = new QtWebSocketClient({
                    url: url,
                    tags: tags,
                    clientInfo: 'Vanilla JS Demo v1.0.0'
                });
                
                // Event listeners
                client.addEventListener('connect', (deviceId) => {
                    deviceIdEl.textContent = deviceId;
                    addMessage(`Connected with device ID: ${deviceId}`, 'system');
                    hideError();
                });
                
                client.addEventListener('disconnect', (reason) => {
                    addMessage(`Disconnected: ${reason}`, 'system');
                });
                
                client.addEventListener('error', (error) => {
                    showError(error);
                    addMessage(`Error: ${error.message}`, 'system');
                });
                
                client.addEventListener('stateChange', (state) => {
                    updateUI(state);
                });
                
                client.addEventListener('broadcast', (data) => {
                    const content = new TextDecoder().decode(data);
                    addMessage(`[Broadcast] ${content}`, 'received');
                });
                
                client.addEventListener('unicast', (data) => {
                    const content = new TextDecoder().decode(data);
                    addMessage(`[Unicast] ${content}`, 'received');
                });
                
                client.addEventListener('tagcast', (data, tags) => {
                    const content = new TextDecoder().decode(data);
                    addMessage(`[Tagcast: ${tags.join(', ')}] ${content}`, 'received');
                });
                
                // Connect
                client.connect();
                
            } catch (error) {
                showError(error);
                addMessage(`Failed to connect: ${error.message}`, 'system');
            }
        }
        
        // Disconnect from WebSocket
        function disconnect() {
            if (client) {
                client.disconnect();
                client = null;
            }
        }
        
        // Send message
        function sendMessage() {
            if (!client || !isConnected) return;
            
            const message = messageInputEl.value.trim();
            if (!message) return;
            
            const messageType = messageTypeEl.value;
            
            try {
                let sentMessage = '';
                
                switch (messageType) {
                    case 'broadcast':
                        client.sendBroadcast(message);
                        sentMessage = `[Broadcast] ${message}`;
                        break;
                    case 'unicast':
                        const targetDevice = targetDeviceEl.value.trim();
                        if (!targetDevice) {
                            alert('Please enter target device ID');
                            return;
                        }
                        client.sendUnicast(targetDevice, message);
                        sentMessage = `[Unicast to ${targetDevice}] ${message}`;
                        break;
                    case 'tagcast':
                        const targetTags = targetTagsEl.value.split(',').map(tag => tag.trim()).filter(tag => tag);
                        if (targetTags.length === 0) {
                            alert('Please enter target tags');
                            return;
                        }
                        client.sendTagcast(targetTags, message);
                        sentMessage = `[Tagcast to ${targetTags.join(', ')}] ${message}`;
                        break;
                }
                
                addMessage(sentMessage, 'sent');
                messageInputEl.value = '';
                
            } catch (error) {
                showError(error);
                addMessage(`Failed to send message: ${error.message}`, 'system');
            }
        }
        
        // Clear messages
        function clearMessages() {
            messagesEl.innerHTML = '';
        }
        
        // Update message type UI
        function updateMessageTypeUI() {
            const messageType = messageTypeEl.value;
            
            targetDeviceLabel.style.display = messageType === 'unicast' ? 'block' : 'none';
            targetTagsLabel.style.display = messageType === 'tagcast' ? 'block' : 'none';
        }
        
        // Event listeners
        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);
        sendBtn.addEventListener('click', sendMessage);
        clearMessagesBtn.addEventListener('click', clearMessages);
        messageTypeEl.addEventListener('change', updateMessageTypeUI);
        
        messageInputEl.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
        
        // Initialize UI
        updateUI('disconnected');
        updateMessageTypeUI();
    </script>
</body>
</html>
