import React, { useState, useEffect, useRef } from 'react';
import {
  useQtWebSocket,
  useQtWebSocketEvent,
  useQtWebSocketState,
  ConnectionState
} from 'qt-websocket-hub-js';

interface ChatMessage {
  id: string;
  content: string;
  timestamp: Date;
  type: 'sent' | 'received' | 'system';
  deviceId?: string;
}

function App() {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [targetDeviceId, setTargetDeviceId] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>(['general']);
  const [messageType, setMessageType] = useState<'broadcast' | 'unicast' | 'tagcast'>('broadcast');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // WebSocket connection
  const ws = useQtWebSocket({
    url: 'ws://localhost:8080/api/v1/ws',
    tags: ['chat', 'general', 'user'],
    clientInfo: 'React Chat App v1.0.0',
    enabled: true,
  });

  const state = useQtWebSocketState(ws);

  // Auto-scroll to bottom
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Add system message
  const addSystemMessage = (content: string) => {
    const message: ChatMessage = {
      id: Date.now().toString(),
      content,
      timestamp: new Date(),
      type: 'system',
    };
    setMessages(prev => [...prev, message]);
  };

  // WebSocket event handlers
  useQtWebSocketEvent(ws, 'connect', (deviceId) => {
    addSystemMessage(`Connected with device ID: ${deviceId}`);
  }, []);

  useQtWebSocketEvent(ws, 'disconnect', (reason) => {
    addSystemMessage(`Disconnected: ${reason}`);
  }, []);

  useQtWebSocketEvent(ws, 'error', (error) => {
    addSystemMessage(`Error: ${error.message}`);
  }, []);

  useQtWebSocketEvent(ws, 'broadcast', (data) => {
    const content = new TextDecoder().decode(data);
    const message: ChatMessage = {
      id: Date.now().toString(),
      content: `[Broadcast] ${content}`,
      timestamp: new Date(),
      type: 'received',
    };
    setMessages(prev => [...prev, message]);
  }, []);

  useQtWebSocketEvent(ws, 'unicast', (data) => {
    const content = new TextDecoder().decode(data);
    const message: ChatMessage = {
      id: Date.now().toString(),
      content: `[Unicast] ${content}`,
      timestamp: new Date(),
      type: 'received',
    };
    setMessages(prev => [...prev, message]);
  }, []);

  useQtWebSocketEvent(ws, 'tagcast', (data, tags) => {
    const content = new TextDecoder().decode(data);
    const message: ChatMessage = {
      id: Date.now().toString(),
      content: `[Tagcast: ${tags.join(', ')}] ${content}`,
      timestamp: new Date(),
      type: 'received',
    };
    setMessages(prev => [...prev, message]);
  }, []);

  // Send message
  const sendMessage = () => {
    if (!inputValue.trim() || !ws.isConnected) return;

    try {
      let sentMessage = '';
      
      switch (messageType) {
        case 'broadcast':
          ws.sendBroadcast(inputValue);
          sentMessage = `[Broadcast] ${inputValue}`;
          break;
        case 'unicast':
          if (!targetDeviceId.trim()) {
            alert('Please enter target device ID for unicast message');
            return;
          }
          ws.sendUnicast(targetDeviceId, inputValue);
          sentMessage = `[Unicast to ${targetDeviceId}] ${inputValue}`;
          break;
        case 'tagcast':
          if (selectedTags.length === 0) {
            alert('Please select at least one tag for tagcast message');
            return;
          }
          ws.sendTagcast(selectedTags, inputValue);
          sentMessage = `[Tagcast to ${selectedTags.join(', ')}] ${inputValue}`;
          break;
      }

      const message: ChatMessage = {
        id: Date.now().toString(),
        content: sentMessage,
        timestamp: new Date(),
        type: 'sent',
        deviceId: ws.deviceId || undefined,
      };

      setMessages(prev => [...prev, message]);
      setInputValue('');
    } catch (error) {
      addSystemMessage(`Failed to send message: ${error}`);
    }
  };

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  // Toggle tag selection
  const toggleTag = (tag: string) => {
    setSelectedTags(prev => 
      prev.includes(tag) 
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  const availableTags = ['general', 'admin', 'support', 'notifications'];

  return (
    <div style={{ maxWidth: '800px', margin: '0 auto', padding: '20px' }}>
      <h1>Qt WebSocket Chat Demo</h1>
      
      {/* Connection Status */}
      <div style={{ 
        padding: '10px', 
        marginBottom: '20px', 
        backgroundColor: state.isConnected ? '#d4edda' : '#f8d7da',
        border: `1px solid ${state.isConnected ? '#c3e6cb' : '#f5c6cb'}`,
        borderRadius: '4px'
      }}>
        <div><strong>Status:</strong> {state.state}</div>
        <div><strong>Device ID:</strong> {state.deviceId || 'Not assigned'}</div>
        {state.error && <div><strong>Error:</strong> {state.error.message}</div>}
        <div style={{ marginTop: '10px' }}>
          <button onClick={ws.connect} disabled={state.isConnected || state.isConnecting}>
            Connect
          </button>
          <button onClick={ws.disconnect} disabled={!state.isConnected} style={{ marginLeft: '10px' }}>
            Disconnect
          </button>
        </div>
      </div>

      {/* Message Type Selection */}
      <div style={{ marginBottom: '20px', padding: '10px', border: '1px solid #ddd', borderRadius: '4px' }}>
        <div style={{ marginBottom: '10px' }}>
          <strong>Message Type:</strong>
          <label style={{ marginLeft: '10px' }}>
            <input
              type="radio"
              value="broadcast"
              checked={messageType === 'broadcast'}
              onChange={(e) => setMessageType(e.target.value as any)}
            />
            Broadcast
          </label>
          <label style={{ marginLeft: '10px' }}>
            <input
              type="radio"
              value="unicast"
              checked={messageType === 'unicast'}
              onChange={(e) => setMessageType(e.target.value as any)}
            />
            Unicast
          </label>
          <label style={{ marginLeft: '10px' }}>
            <input
              type="radio"
              value="tagcast"
              checked={messageType === 'tagcast'}
              onChange={(e) => setMessageType(e.target.value as any)}
            />
            Tagcast
          </label>
        </div>

        {messageType === 'unicast' && (
          <div style={{ marginBottom: '10px' }}>
            <label>
              <strong>Target Device ID:</strong>
              <input
                type="text"
                value={targetDeviceId}
                onChange={(e) => setTargetDeviceId(e.target.value)}
                placeholder="Enter target device ID"
                style={{ marginLeft: '10px', padding: '5px' }}
              />
            </label>
          </div>
        )}

        {messageType === 'tagcast' && (
          <div style={{ marginBottom: '10px' }}>
            <strong>Tags:</strong>
            {availableTags.map(tag => (
              <label key={tag} style={{ marginLeft: '10px' }}>
                <input
                  type="checkbox"
                  checked={selectedTags.includes(tag)}
                  onChange={() => toggleTag(tag)}
                />
                {tag}
              </label>
            ))}
          </div>
        )}
      </div>

      {/* Messages */}
      <div style={{ 
        height: '400px', 
        overflowY: 'auto', 
        border: '1px solid #ddd', 
        padding: '10px',
        marginBottom: '20px',
        backgroundColor: '#f9f9f9'
      }}>
        {messages.map((message) => (
          <div
            key={message.id}
            style={{
              marginBottom: '10px',
              padding: '8px',
              borderRadius: '4px',
              backgroundColor: 
                message.type === 'sent' ? '#007bff' :
                message.type === 'system' ? '#6c757d' : '#28a745',
              color: 'white',
              textAlign: message.type === 'sent' ? 'right' : 'left'
            }}
          >
            <div>{message.content}</div>
            <div style={{ fontSize: '0.8em', opacity: 0.8 }}>
              {message.timestamp.toLocaleTimeString()}
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div style={{ display: 'flex', gap: '10px' }}>
        <textarea
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="Type your message..."
          disabled={!ws.isConnected}
          style={{ 
            flex: 1, 
            padding: '10px', 
            border: '1px solid #ddd', 
            borderRadius: '4px',
            resize: 'vertical',
            minHeight: '60px'
          }}
        />
        <button
          onClick={sendMessage}
          disabled={!ws.isConnected || !inputValue.trim()}
          style={{
            padding: '10px 20px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: ws.isConnected ? 'pointer' : 'not-allowed'
          }}
        >
          Send
        </button>
      </div>

      {/* Clear Messages */}
      <div style={{ marginTop: '10px', textAlign: 'center' }}>
        <button
          onClick={() => setMessages([])}
          style={{
            padding: '5px 15px',
            backgroundColor: '#6c757d',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Clear Messages
        </button>
      </div>
    </div>
  );
}

export default App;
