# WebSocket Hub - 分布式WebSocket服务中心

一个高性能、可扩展的分布式WebSocket服务中心，使用Golang开发，支持Kafka作为消息中间件。

## 特性

- 🚀 **高性能**: 基于Gorilla WebSocket，支持大量并发连接
- 🔐 **安全**: 服务端生成设备ID，防止恶意伪造和直连攻击
- 📦 **二进制消息**: 使用自定义二进制协议，避免JSON双重解析
- 🏷️ **标签系统**: 支持连接标签，可基于标签进行消息路由
- 🌐 **分布式**: 使用Kafka实现多节点消息分发
- 📊 **监控**: 提供丰富的HTTP API和统计信息
- 🔧 **可配置**: 支持配置文件和环境变量配置

## 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   WebSocket     │    │   WebSocket     │    │   WebSocket     │
│   Client 1      │    │   Client 2      │    │   Client 3      │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │    WebSocket Hub Node     │
                    │  ┌─────────────────────┐  │
                    │  │  Connection Mgr     │  │
                    │  │  Device Manager     │  │
                    │  │  Message Router     │  │
                    │  │  HTTP API Server    │  │
                    │  └─────────────────────┘  │
                    └─────────────┬─────────────┘
                                  │
                    ┌─────────────┴─────────────┐
                    │      Kafka Cluster       │
                    │   (Message Middleware)    │
                    └─────────────┬─────────────┘
                                  │
          ┌───────────────────────┼───────────────────────┐
          │                       │                       │
┌─────────┴─────────┐   ┌─────────┴─────────┐   ┌─────────┴─────────┐
│ WebSocket Hub     │   │ WebSocket Hub     │   │ WebSocket Hub     │
│ Node 2            │   │ Node 3            │   │ Node N            │
└───────────────────┘   └───────────────────┘   └───────────────────┘
```

## 快速开始

### 1. 编译项目

```bash
# 编译服务端
go build -o websocket-hub cmd/server/main.go

# 编译客户端示例
go build -o websocket-client examples/client/main.go
```

### 2. 配置文件

复制示例配置文件并修改：

```bash
cp config.example.json config.json
```

主要配置项：
- `server.node_id`: 节点ID，集群中唯一
- `server.port`: 服务端口
- `kafka.enabled`: 是否启用Kafka分布式功能
- `kafka.brokers`: Kafka集群地址
- `device.secret_key`: 设备ID签名密钥（生产环境必须修改）

### 3. 启动服务

```bash
# 单机模式（不使用Kafka）
./websocket-hub -config config.json

# 分布式模式（需要先启动Kafka）
# 修改config.json中kafka.enabled为true
./websocket-hub -config config.json
```

### 4. 测试连接

```bash
# 启动客户端
./websocket-client -addr localhost:8080 -tags "user,mobile"

# 或者使用curl测试HTTP API
curl http://localhost:8080/api/v1/stats
```

## API 文档

### WebSocket 连接

**端点**: `ws://localhost:8080/api/v1/ws`

**握手流程**:
1. 客户端连接WebSocket
2. 发送握手消息（包含设备ID和标签）
3. 服务端验证并响应
4. 开始消息通信

### HTTP API

#### 生成设备ID
```http
POST /api/v1/devices
Content-Type: application/json

{
  "tags": ["user", "mobile"],
  "remote_addr": "*************",
  "user_agent": "MyApp/1.0"
}
```

#### 发送消息
```http
POST /api/v1/messages
Content-Type: application/json

{
  "type": "unicast",
  "target_device_id": "WS_xxx_xxx_xxx",
  "data": "SGVsbG8gV29ybGQ=",
  "distributed": false
}
```

#### 获取统计信息
```http
GET /api/v1/stats
```

#### 健康检查
```http
GET /api/v1/health
```

更多API详情请参考 [API文档](docs/api.md)

## 消息协议

### 二进制消息格式

```
┌─────────────────────────────────────────────────────────────┐
│                    Message Header (16 bytes)                │
├─────────┬─────────┬─────────┬─────────┬─────────┬───────────┤
│ Magic   │ Version │ Type    │ Flags   │Reserved │PayloadLen │
│ (2)     │ (1)     │ (1)     │ (1)     │ (1)     │ (4)       │
├─────────┼─────────┴─────────┴─────────┴─────────┼───────────┤
│Timestamp│                                       │ Checksum  │
│ (4)     │                                       │ (2)       │
├─────────┴───────────────────────────────────────┴───────────┤
│                    Payload (Variable)                       │
└─────────────────────────────────────────────────────────────┘
```

### 消息类型

- `0x01`: 握手消息
- `0x02`: 握手响应
- `0x03`: 心跳消息
- `0x04`: 断开连接
- `0x10`: 单播消息
- `0x11`: 广播消息
- `0x12`: 标签组播消息
- `0xF0`: 错误消息
- `0xF1`: 确认消息

## 部署指南

### Docker 部署

```dockerfile
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -o websocket-hub cmd/server/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/websocket-hub .
COPY config.json .
CMD ["./websocket-hub", "-config", "config.json"]
```

### Kubernetes 部署

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: websocket-hub
spec:
  replicas: 3
  selector:
    matchLabels:
      app: websocket-hub
  template:
    metadata:
      labels:
        app: websocket-hub
    spec:
      containers:
      - name: websocket-hub
        image: websocket-hub:latest
        ports:
        - containerPort: 8080
        env:
        - name: KAFKA_ENABLED
          value: "true"
        - name: KAFKA_BROKERS
          value: "kafka:9092"
```

## 性能测试

### 基准测试

```bash
# 运行所有测试
go test ./...

# 运行基准测试
go test -bench=. ./internal/protocol/
go test -bench=. ./internal/device/

# 压力测试
go run examples/stress/main.go -connections 1000 -duration 60s
```

### 性能指标

- **并发连接**: 支持10,000+并发WebSocket连接
- **消息吞吐**: 100,000+ 消息/秒
- **延迟**: < 1ms (本地网络)
- **内存使用**: ~50MB (1000连接)

## 监控和日志

### 日志格式

```json
{
  "timestamp": "2024-01-01T12:00:00Z",
  "level": "info",
  "message": "Client connected",
  "device_id": "WS_xxx_xxx_xxx",
  "remote_addr": "*************:12345",
  "tags": ["user", "mobile"]
}
```

### 监控指标

- 当前连接数
- 总连接数
- 消息收发统计
- 错误计数
- 节点状态

## 开发指南

### 项目结构

```
├── cmd/server/          # 服务端入口
├── examples/client/     # 客户端示例
├── internal/
│   ├── api/            # HTTP API
│   ├── config/         # 配置管理
│   ├── connection/     # 连接管理
│   ├── device/         # 设备管理
│   ├── hub/            # 服务中心
│   ├── kafka/          # Kafka集成
│   ├── logger/         # 日志系统
│   └── protocol/       # 消息协议
├── config.example.json  # 配置示例
└── README.md
```

### 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 联系方式

- 问题反馈: [GitHub Issues](https://github.com/your-repo/websocket-hub/issues)
- 邮箱: <EMAIL>
