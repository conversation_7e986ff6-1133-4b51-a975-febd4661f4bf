#!/bin/bash

# WebSocket Hub 启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
CONFIG_FILE="config.json"
LOG_DIR="logs"
PID_FILE="websocket-hub.pid"

# 函数定义
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Go环境
check_go() {
    if ! command -v go &> /dev/null; then
        log_error "Go is not installed or not in PATH"
        exit 1
    fi
    log_info "Go version: $(go version)"
}

# 编译项目
build() {
    log_info "Building WebSocket Hub..."
    if ! go build -o websocket-hub cmd/server/main.go; then
        log_error "Build failed"
        exit 1
    fi
    
    log_info "Building WebSocket Client..."
    if ! go build -o websocket-client examples/client/main.go; then
        log_error "Client build failed"
        exit 1
    fi
    
    log_info "Build completed successfully"
}

# 运行测试
test() {
    log_info "Running tests..."
    if ! go test ./...; then
        log_error "Tests failed"
        exit 1
    fi
    log_info "All tests passed"
}

# 创建配置文件
create_config() {
    if [ ! -f "$CONFIG_FILE" ]; then
        log_info "Creating default configuration file..."
        cp config.example.json "$CONFIG_FILE"
        log_warn "Please edit $CONFIG_FILE to customize your configuration"
    fi
}

# 创建日志目录
create_log_dir() {
    if [ ! -d "$LOG_DIR" ]; then
        mkdir -p "$LOG_DIR"
        log_info "Created log directory: $LOG_DIR"
    fi
}

# 启动服务器
start() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            log_warn "WebSocket Hub is already running (PID: $PID)"
            return
        else
            log_warn "Removing stale PID file"
            rm -f "$PID_FILE"
        fi
    fi

    log_info "Starting WebSocket Hub..."
    nohup ./websocket-hub -config "$CONFIG_FILE" > "$LOG_DIR/websocket-hub.log" 2>&1 &
    PID=$!
    echo $PID > "$PID_FILE"
    
    # 等待服务启动
    sleep 2
    if ps -p "$PID" > /dev/null 2>&1; then
        log_info "WebSocket Hub started successfully (PID: $PID)"
        log_info "Log file: $LOG_DIR/websocket-hub.log"
    else
        log_error "Failed to start WebSocket Hub"
        rm -f "$PID_FILE"
        exit 1
    fi
}

# 停止服务器
stop() {
    if [ ! -f "$PID_FILE" ]; then
        log_warn "WebSocket Hub is not running"
        return
    fi

    PID=$(cat "$PID_FILE")
    if ps -p "$PID" > /dev/null 2>&1; then
        log_info "Stopping WebSocket Hub (PID: $PID)..."
        kill "$PID"
        
        # 等待进程结束
        for i in {1..10}; do
            if ! ps -p "$PID" > /dev/null 2>&1; then
                break
            fi
            sleep 1
        done
        
        if ps -p "$PID" > /dev/null 2>&1; then
            log_warn "Force killing WebSocket Hub..."
            kill -9 "$PID"
        fi
        
        rm -f "$PID_FILE"
        log_info "WebSocket Hub stopped"
    else
        log_warn "WebSocket Hub is not running"
        rm -f "$PID_FILE"
    fi
}

# 重启服务器
restart() {
    stop
    sleep 1
    start
}

# 查看状态
status() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            log_info "WebSocket Hub is running (PID: $PID)"
            
            # 尝试获取服务状态
            if command -v curl &> /dev/null; then
                echo ""
                echo "Service Health Check:"
                curl -s http://localhost:8080/api/v1/health | jq . 2>/dev/null || curl -s http://localhost:8080/api/v1/health
            fi
        else
            log_warn "WebSocket Hub is not running (stale PID file)"
            rm -f "$PID_FILE"
        fi
    else
        log_warn "WebSocket Hub is not running"
    fi
}

# 查看日志
logs() {
    if [ -f "$LOG_DIR/websocket-hub.log" ]; then
        tail -f "$LOG_DIR/websocket-hub.log"
    else
        log_error "Log file not found: $LOG_DIR/websocket-hub.log"
    fi
}

# 启动客户端
client() {
    local addr="localhost:8080"
    local tags=""
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --addr)
                addr="$2"
                shift 2
                ;;
            --tags)
                tags="$2"
                shift 2
                ;;
            *)
                log_error "Unknown option: $1"
                echo "Usage: $0 client [--addr <address>] [--tags <tags>]"
                exit 1
                ;;
        esac
    done
    
    log_info "Starting WebSocket client..."
    log_info "Server: $addr"
    if [ -n "$tags" ]; then
        log_info "Tags: $tags"
        ./websocket-client -addr "$addr" -tags "$tags"
    else
        ./websocket-client -addr "$addr"
    fi
}

# 显示帮助
help() {
    echo "WebSocket Hub Management Script"
    echo ""
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  build     - Build the project"
    echo "  test      - Run tests"
    echo "  start     - Start the WebSocket Hub server"
    echo "  stop      - Stop the WebSocket Hub server"
    echo "  restart   - Restart the WebSocket Hub server"
    echo "  status    - Show server status"
    echo "  logs      - Show server logs (tail -f)"
    echo "  client    - Start a WebSocket client"
    echo "  help      - Show this help message"
    echo ""
    echo "Client options:"
    echo "  --addr <address>  - Server address (default: localhost:8080)"
    echo "  --tags <tags>     - Comma-separated tags"
    echo ""
    echo "Examples:"
    echo "  $0 build"
    echo "  $0 start"
    echo "  $0 client --addr localhost:8080 --tags user,mobile"
    echo "  $0 logs"
}

# 主逻辑
main() {
    case "${1:-help}" in
        build)
            check_go
            build
            ;;
        test)
            check_go
            test
            ;;
        start)
            create_config
            create_log_dir
            start
            ;;
        stop)
            stop
            ;;
        restart)
            restart
            ;;
        status)
            status
            ;;
        logs)
            logs
            ;;
        client)
            shift
            client "$@"
            ;;
        help|--help|-h)
            help
            ;;
        *)
            log_error "Unknown command: $1"
            help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
