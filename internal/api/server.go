package api

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"websocket-hub/internal/hub"
	"websocket-hub/internal/kafka"
)

// Server HTTP API服务器
type Server struct {
	hub    *hub.Hub
	logger *zap.Logger
	router *gin.Engine
}

// DeviceIDResponse 设备ID响应
type DeviceIDResponse struct {
	DeviceID  string `json:"device_id"`
	Timestamp int64  `json:"timestamp"`
}

// MessageRequest 消息发送请求
type MessageRequest struct {
	Type           string   `json:"type"`           // unicast, broadcast, tagcast
	TargetDeviceID string   `json:"target_device_id,omitempty"`
	Tags           []string `json:"tags,omitempty"`
	Data           []byte   `json:"data"`
	Distributed    bool     `json:"distributed,omitempty"` // 是否分布式发送
}

// ConnectionInfo 连接信息
type ConnectionInfo struct {
	DeviceID   string   `json:"device_id"`
	Tags       []string `json:"tags"`
	RemoteAddr string   `json:"remote_addr"`
	UserAgent  string   `json:"user_agent"`
	CreatedAt  int64    `json:"created_at"`
	LastSeen   int64    `json:"last_seen"`
}

// StatsResponse 统计信息响应
type StatsResponse struct {
	NodeID              string `json:"node_id"`
	StartTime           int64  `json:"start_time"`
	TotalConnections    int64  `json:"total_connections"`
	CurrentConnections  int    `json:"current_connections"`
	MessagesReceived    int64  `json:"messages_received"`
	MessagesSent        int64  `json:"messages_sent"`
	DistributedMessages int64  `json:"distributed_messages"`
	Errors              int64  `json:"errors"`
}

// NodesResponse 节点信息响应
type NodesResponse struct {
	Nodes []NodeInfo `json:"nodes"`
}

// NodeInfo 节点信息
type NodeInfo struct {
	NodeID      string   `json:"node_id"`
	Address     string   `json:"address"`
	Port        int      `json:"port"`
	StartTime   int64    `json:"start_time"`
	Connections int      `json:"connections"`
	Tags        []string `json:"tags"`
}

// NewServer 创建HTTP API服务器
func NewServer(hub *hub.Hub, logger *zap.Logger) *Server {
	// 设置Gin为发布模式
	gin.SetMode(gin.ReleaseMode)
	
	server := &Server{
		hub:    hub,
		logger: logger,
		router: gin.New(),
	}

	// 添加中间件
	server.router.Use(gin.Recovery())
	server.router.Use(server.loggingMiddleware())
	server.router.Use(server.corsMiddleware())

	// 注册路由
	server.setupRoutes()

	return server
}

// setupRoutes 设置路由
func (s *Server) setupRoutes() {
	api := s.router.Group("/api/v1")
	{
		// WebSocket连接端点
		api.GET("/ws", s.handleWebSocket)
		
		// 设备管理
		api.POST("/devices", s.generateDeviceID)
		api.GET("/devices/:device_id", s.getDeviceInfo)
		api.DELETE("/devices/:device_id", s.disconnectDevice)
		
		// 消息发送
		api.POST("/messages", s.sendMessage)
		
		// 连接管理
		api.GET("/connections", s.listConnections)
		api.GET("/connections/count", s.getConnectionCount)
		api.GET("/connections/tags/:tag", s.getConnectionsByTag)
		
		// 统计信息
		api.GET("/stats", s.getStats)
		api.GET("/health", s.healthCheck)
		
		// 分布式节点信息
		api.GET("/nodes", s.getNodes)
	}
}

// loggingMiddleware 日志中间件
func (s *Server) loggingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := c.Request.Context().Value("start_time")
		c.Next()
		
		s.logger.Info("HTTP Request",
			zap.String("method", c.Request.Method),
			zap.String("path", c.Request.URL.Path),
			zap.Int("status", c.Writer.Status()),
			zap.String("client_ip", c.ClientIP()),
			zap.String("user_agent", c.Request.UserAgent()),
		)
		
		if start != nil {
			// 可以添加请求时间统计
		}
	}
}

// corsMiddleware CORS中间件
func (s *Server) corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")
		
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}
		
		c.Next()
	}
}

// handleWebSocket 处理WebSocket连接
func (s *Server) handleWebSocket(c *gin.Context) {
	s.hub.GetConnectionManager().HandleWebSocket(c.Writer, c.Request)
}

// generateDeviceID 生成设备ID
func (s *Server) generateDeviceID(c *gin.Context) {
	var req struct {
		Tags       []string `json:"tags"`
		RemoteAddr string   `json:"remote_addr,omitempty"`
		UserAgent  string   `json:"user_agent,omitempty"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// 使用请求中的地址信息，如果没有则使用实际的客户端信息
	remoteAddr := req.RemoteAddr
	if remoteAddr == "" {
		remoteAddr = c.ClientIP()
	}
	
	userAgent := req.UserAgent
	if userAgent == "" {
		userAgent = c.Request.UserAgent()
	}

	// 生成设备ID
	deviceInfo, err := s.hub.GetConnectionManager().GetDeviceManager().RegisterDevice(
		req.Tags,
		remoteAddr,
		userAgent,
	)
	if err != nil {
		s.logger.Error("Failed to generate device ID", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate device ID"})
		return
	}

	response := DeviceIDResponse{
		DeviceID:  deviceInfo.ID,
		Timestamp: deviceInfo.CreatedAt.Unix(),
	}

	c.JSON(http.StatusOK, response)
}

// getDeviceInfo 获取设备信息
func (s *Server) getDeviceInfo(c *gin.Context) {
	deviceID := c.Param("device_id")
	
	deviceInfo, exists := s.hub.GetConnectionManager().GetDeviceManager().GetDevice(deviceID)
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Device not found"})
		return
	}

	response := ConnectionInfo{
		DeviceID:   deviceInfo.ID,
		Tags:       deviceInfo.Tags,
		RemoteAddr: deviceInfo.RemoteAddr,
		UserAgent:  deviceInfo.UserAgent,
		CreatedAt:  deviceInfo.CreatedAt.Unix(),
		LastSeen:   deviceInfo.LastSeen.Unix(),
	}

	c.JSON(http.StatusOK, response)
}

// disconnectDevice 断开设备连接
func (s *Server) disconnectDevice(c *gin.Context) {
	deviceID := c.Param("device_id")
	
	if err := s.hub.GetConnectionManager().CloseConnection(deviceID); err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Device not connected"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Device disconnected"})
}

// sendMessage 发送消息
func (s *Server) sendMessage(c *gin.Context) {
	var req MessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	var err error
	var sent int

	switch req.Type {
	case "unicast":
		if req.TargetDeviceID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "target_device_id is required for unicast"})
			return
		}
		
		if req.Distributed {
			// 分布式单播
			data := &kafka.DistributedUnicastData{
				TargetDeviceID: req.TargetDeviceID,
				Payload:        req.Data,
			}
			err = s.hub.SendDistributedMessage("unicast", "", data)
		} else {
			// 本地单播
			err = s.hub.GetConnectionManager().SendToDevice(req.TargetDeviceID, req.Data)
			if err == nil {
				sent = 1
			}
		}

	case "broadcast":
		if req.Distributed {
			// 分布式广播
			data := &kafka.DistributedBroadcastData{
				Payload: req.Data,
			}
			err = s.hub.SendDistributedMessage("broadcast", "", data)
		} else {
			// 本地广播
			sent = s.hub.GetConnectionManager().Broadcast(req.Data)
		}

	case "tagcast":
		if len(req.Tags) == 0 {
			c.JSON(http.StatusBadRequest, gin.H{"error": "tags are required for tagcast"})
			return
		}
		
		if req.Distributed {
			// 分布式标签组播
			data := &kafka.DistributedTagcastData{
				Tags:    req.Tags,
				Payload: req.Data,
			}
			err = s.hub.SendDistributedMessage("tagcast", "", data)
		} else {
			// 本地标签组播
			for _, tag := range req.Tags {
				sent += s.hub.GetConnectionManager().SendToTag(tag, req.Data)
			}
		}

	default:
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid message type"})
		return
	}

	if err != nil {
		s.logger.Error("Failed to send message", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to send message"})
		return
	}

	response := gin.H{"message": "Message sent successfully"}
	if !req.Distributed {
		response["sent_count"] = sent
	}

	c.JSON(http.StatusOK, response)
}

// listConnections 列出所有连接
func (s *Server) listConnections(c *gin.Context) {
	devices := s.hub.GetConnectionManager().GetDeviceManager().GetAllDevices()
	
	connections := make([]ConnectionInfo, 0, len(devices))
	for _, device := range devices {
		// 只返回当前连接的设备
		if _, exists := s.hub.GetConnectionManager().GetConnectionInfo(device.ID); exists {
			connections = append(connections, ConnectionInfo{
				DeviceID:   device.ID,
				Tags:       device.Tags,
				RemoteAddr: device.RemoteAddr,
				UserAgent:  device.UserAgent,
				CreatedAt:  device.CreatedAt.Unix(),
				LastSeen:   device.LastSeen.Unix(),
			})
		}
	}

	c.JSON(http.StatusOK, gin.H{"connections": connections})
}

// getConnectionCount 获取连接数量
func (s *Server) getConnectionCount(c *gin.Context) {
	count := s.hub.GetConnectionManager().GetConnectionCount()
	c.JSON(http.StatusOK, gin.H{"count": count})
}

// getConnectionsByTag 获取指定标签的连接数量
func (s *Server) getConnectionsByTag(c *gin.Context) {
	tag := c.Param("tag")
	count := s.hub.GetConnectionManager().GetConnectionsByTag(tag)
	c.JSON(http.StatusOK, gin.H{"tag": tag, "count": count})
}

// getStats 获取统计信息
func (s *Server) getStats(c *gin.Context) {
	stats := s.hub.GetStats()
	
	response := StatsResponse{
		NodeID:              s.hub.GetNodeID(),
		StartTime:           stats.StartTime.Unix(),
		TotalConnections:    stats.TotalConnections,
		CurrentConnections:  stats.CurrentConnections,
		MessagesReceived:    stats.MessagesReceived,
		MessagesSent:        stats.MessagesSent,
		DistributedMessages: stats.DistributedMessages,
		Errors:              stats.Errors,
	}

	c.JSON(http.StatusOK, response)
}

// healthCheck 健康检查
func (s *Server) healthCheck(c *gin.Context) {
	healthy := s.hub.IsHealthy()
	status := "healthy"
	httpStatus := http.StatusOK
	
	if !healthy {
		status = "unhealthy"
		httpStatus = http.StatusServiceUnavailable
	}

	c.JSON(httpStatus, gin.H{
		"status":      status,
		"node_id":     s.hub.GetNodeID(),
		"timestamp":   c.Request.Context().Value("timestamp"),
		"connections": s.hub.GetConnectionManager().GetConnectionCount(),
	})
}

// getNodes 获取分布式节点信息
func (s *Server) getNodes(c *gin.Context) {
	kafkaNodes := s.hub.GetNodes()
	
	nodes := make([]NodeInfo, 0, len(kafkaNodes))
	for _, node := range kafkaNodes {
		nodes = append(nodes, NodeInfo{
			NodeID:      node.NodeID,
			Address:     node.Address,
			Port:        node.Port,
			StartTime:   node.StartTime.Unix(),
			Connections: node.Connections,
			Tags:        node.Tags,
		})
	}

	response := NodesResponse{Nodes: nodes}
	c.JSON(http.StatusOK, response)
}

// GetRouter 获取Gin路由器
func (s *Server) GetRouter() *gin.Engine {
	return s.router
}

// Start 启动HTTP服务器
func (s *Server) Start(addr string) error {
	s.logger.Info("Starting HTTP server", zap.String("address", addr))
	return s.router.Run(addr)
}
