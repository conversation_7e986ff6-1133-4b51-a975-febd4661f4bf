package kafka

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/IBM/sarama"
	"go.uber.org/zap"
)

// MessageType Kafka消息类型
type MessageType string

const (
	// 分布式消息类型
	MsgTypeDistributedUnicast   MessageType = "distributed_unicast"
	MsgTypeDistributedBroadcast MessageType = "distributed_broadcast"
	MsgTypeDistributedTagcast   MessageType = "distributed_tagcast"
	
	// 系统消息类型
	MsgTypeNodeJoin  MessageType = "node_join"
	MsgTypeNodeLeave MessageType = "node_leave"
	MsgTypeNodeSync  MessageType = "node_sync"
)

// KafkaMessage Kafka消息结构
type KafkaMessage struct {
	Type      MessageType `json:"type"`
	NodeID    string      `json:"node_id"`
	Timestamp int64       `json:"timestamp"`
	Data      interface{} `json:"data"`
}

// DistributedUnicastData 分布式单播数据
type DistributedUnicastData struct {
	TargetDeviceID string `json:"target_device_id"`
	SourceDeviceID string `json:"source_device_id"`
	Payload        []byte `json:"payload"`
}

// DistributedBroadcastData 分布式广播数据
type DistributedBroadcastData struct {
	SourceDeviceID string `json:"source_device_id"`
	Payload        []byte `json:"payload"`
}

// DistributedTagcastData 分布式标签组播数据
type DistributedTagcastData struct {
	Tags           []string `json:"tags"`
	SourceDeviceID string   `json:"source_device_id"`
	Payload        []byte   `json:"payload"`
}

// NodeInfo 节点信息
type NodeInfo struct {
	NodeID      string    `json:"node_id"`
	Address     string    `json:"address"`
	Port        int       `json:"port"`
	StartTime   time.Time `json:"start_time"`
	Connections int       `json:"connections"`
	Tags        []string  `json:"tags"`
}

// MessageHandler 消息处理器接口
type MessageHandler interface {
	HandleDistributedUnicast(data *DistributedUnicastData) error
	HandleDistributedBroadcast(data *DistributedBroadcastData) error
	HandleDistributedTagcast(data *DistributedTagcastData) error
	HandleNodeJoin(nodeInfo *NodeInfo) error
	HandleNodeLeave(nodeID string) error
	HandleNodeSync(nodes []*NodeInfo) error
}

// KafkaClient Kafka客户端
type KafkaClient struct {
	brokers       []string
	nodeID        string
	topic         string
	producer      sarama.SyncProducer
	consumer      sarama.Consumer
	consumerGroup sarama.ConsumerGroup
	handler       MessageHandler
	logger        *zap.Logger
	
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
}

// NewKafkaClient 创建Kafka客户端
func NewKafkaClient(brokers []string, nodeID, topic string, handler MessageHandler, logger *zap.Logger) (*KafkaClient, error) {
	ctx, cancel := context.WithCancel(context.Background())
	
	client := &KafkaClient{
		brokers: brokers,
		nodeID:  nodeID,
		topic:   topic,
		handler: handler,
		logger:  logger,
		ctx:     ctx,
		cancel:  cancel,
	}

	if err := client.initProducer(); err != nil {
		cancel()
		return nil, fmt.Errorf("failed to init producer: %w", err)
	}

	if err := client.initConsumer(); err != nil {
		cancel()
		return nil, fmt.Errorf("failed to init consumer: %w", err)
	}

	return client, nil
}

// initProducer 初始化生产者
func (kc *KafkaClient) initProducer() error {
	config := sarama.NewConfig()
	config.Producer.RequiredAcks = sarama.WaitForAll
	config.Producer.Retry.Max = 5
	config.Producer.Return.Successes = true
	config.Producer.Compression = sarama.CompressionSnappy

	producer, err := sarama.NewSyncProducer(kc.brokers, config)
	if err != nil {
		return fmt.Errorf("failed to create producer: %w", err)
	}

	kc.producer = producer
	return nil
}

// initConsumer 初始化消费者
func (kc *KafkaClient) initConsumer() error {
	config := sarama.NewConfig()
	config.Consumer.Group.Rebalance.Strategy = sarama.BalanceStrategyRoundRobin
	config.Consumer.Offsets.Initial = sarama.OffsetNewest
	config.Consumer.Group.Session.Timeout = 10 * time.Second
	config.Consumer.Group.Heartbeat.Interval = 3 * time.Second

	consumerGroup, err := sarama.NewConsumerGroup(kc.brokers, kc.nodeID, config)
	if err != nil {
		return fmt.Errorf("failed to create consumer group: %w", err)
	}

	kc.consumerGroup = consumerGroup
	return nil
}

// Start 启动Kafka客户端
func (kc *KafkaClient) Start() error {
	// 启动消费者
	kc.wg.Add(1)
	go func() {
		defer kc.wg.Done()
		kc.runConsumer()
	}()

	// 发送节点加入消息
	nodeInfo := &NodeInfo{
		NodeID:      kc.nodeID,
		StartTime:   time.Now(),
		Connections: 0,
	}

	if err := kc.SendNodeJoin(nodeInfo); err != nil {
		kc.logger.Error("Failed to send node join message", zap.Error(err))
	}

	kc.logger.Info("Kafka client started", zap.String("node_id", kc.nodeID))
	return nil
}

// Stop 停止Kafka客户端
func (kc *KafkaClient) Stop() error {
	// 发送节点离开消息
	if err := kc.SendNodeLeave(); err != nil {
		kc.logger.Error("Failed to send node leave message", zap.Error(err))
	}

	// 取消上下文
	kc.cancel()

	// 等待消费者停止
	kc.wg.Wait()

	// 关闭生产者和消费者
	if kc.producer != nil {
		kc.producer.Close()
	}
	if kc.consumerGroup != nil {
		kc.consumerGroup.Close()
	}

	kc.logger.Info("Kafka client stopped")
	return nil
}

// runConsumer 运行消费者
func (kc *KafkaClient) runConsumer() {
	for {
		select {
		case <-kc.ctx.Done():
			return
		default:
			if err := kc.consumerGroup.Consume(kc.ctx, []string{kc.topic}, kc); err != nil {
				kc.logger.Error("Consumer error", zap.Error(err))
				time.Sleep(time.Second)
			}
		}
	}
}

// Setup 实现sarama.ConsumerGroupHandler接口
func (kc *KafkaClient) Setup(sarama.ConsumerGroupSession) error {
	return nil
}

// Cleanup 实现sarama.ConsumerGroupHandler接口
func (kc *KafkaClient) Cleanup(sarama.ConsumerGroupSession) error {
	return nil
}

// ConsumeClaim 实现sarama.ConsumerGroupHandler接口
func (kc *KafkaClient) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for {
		select {
		case message := <-claim.Messages():
			if message == nil {
				return nil
			}

			if err := kc.handleMessage(message); err != nil {
				kc.logger.Error("Failed to handle message", zap.Error(err))
			} else {
				session.MarkMessage(message, "")
			}

		case <-session.Context().Done():
			return nil
		}
	}
}

// handleMessage 处理接收到的消息
func (kc *KafkaClient) handleMessage(message *sarama.ConsumerMessage) error {
	var kafkaMsg KafkaMessage
	if err := json.Unmarshal(message.Value, &kafkaMsg); err != nil {
		return fmt.Errorf("failed to unmarshal kafka message: %w", err)
	}

	// 忽略自己发送的消息
	if kafkaMsg.NodeID == kc.nodeID {
		return nil
	}

	switch kafkaMsg.Type {
	case MsgTypeDistributedUnicast:
		var data DistributedUnicastData
		if err := kc.unmarshalData(kafkaMsg.Data, &data); err != nil {
			return err
		}
		return kc.handler.HandleDistributedUnicast(&data)

	case MsgTypeDistributedBroadcast:
		var data DistributedBroadcastData
		if err := kc.unmarshalData(kafkaMsg.Data, &data); err != nil {
			return err
		}
		return kc.handler.HandleDistributedBroadcast(&data)

	case MsgTypeDistributedTagcast:
		var data DistributedTagcastData
		if err := kc.unmarshalData(kafkaMsg.Data, &data); err != nil {
			return err
		}
		return kc.handler.HandleDistributedTagcast(&data)

	case MsgTypeNodeJoin:
		var nodeInfo NodeInfo
		if err := kc.unmarshalData(kafkaMsg.Data, &nodeInfo); err != nil {
			return err
		}
		return kc.handler.HandleNodeJoin(&nodeInfo)

	case MsgTypeNodeLeave:
		nodeID, ok := kafkaMsg.Data.(string)
		if !ok {
			return fmt.Errorf("invalid node leave data")
		}
		return kc.handler.HandleNodeLeave(nodeID)

	case MsgTypeNodeSync:
		var nodes []*NodeInfo
		if err := kc.unmarshalData(kafkaMsg.Data, &nodes); err != nil {
			return err
		}
		return kc.handler.HandleNodeSync(nodes)

	default:
		kc.logger.Warn("Unknown message type", zap.String("type", string(kafkaMsg.Type)))
		return nil
	}
}

// unmarshalData 反序列化数据
func (kc *KafkaClient) unmarshalData(data interface{}, target interface{}) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("failed to marshal data: %w", err)
	}
	
	if err := json.Unmarshal(jsonData, target); err != nil {
		return fmt.Errorf("failed to unmarshal data: %w", err)
	}
	
	return nil
}

// sendMessage 发送消息到Kafka
func (kc *KafkaClient) sendMessage(msgType MessageType, data interface{}) error {
	kafkaMsg := KafkaMessage{
		Type:      msgType,
		NodeID:    kc.nodeID,
		Timestamp: time.Now().Unix(),
		Data:      data,
	}

	msgBytes, err := json.Marshal(kafkaMsg)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	msg := &sarama.ProducerMessage{
		Topic: kc.topic,
		Value: sarama.StringEncoder(msgBytes),
	}

	_, _, err = kc.producer.SendMessage(msg)
	if err != nil {
		return fmt.Errorf("failed to send message: %w", err)
	}

	return nil
}

// SendDistributedUnicast 发送分布式单播消息
func (kc *KafkaClient) SendDistributedUnicast(targetDeviceID, sourceDeviceID string, payload []byte) error {
	data := &DistributedUnicastData{
		TargetDeviceID: targetDeviceID,
		SourceDeviceID: sourceDeviceID,
		Payload:        payload,
	}
	return kc.sendMessage(MsgTypeDistributedUnicast, data)
}

// SendDistributedBroadcast 发送分布式广播消息
func (kc *KafkaClient) SendDistributedBroadcast(sourceDeviceID string, payload []byte) error {
	data := &DistributedBroadcastData{
		SourceDeviceID: sourceDeviceID,
		Payload:        payload,
	}
	return kc.sendMessage(MsgTypeDistributedBroadcast, data)
}

// SendDistributedTagcast 发送分布式标签组播消息
func (kc *KafkaClient) SendDistributedTagcast(tags []string, sourceDeviceID string, payload []byte) error {
	data := &DistributedTagcastData{
		Tags:           tags,
		SourceDeviceID: sourceDeviceID,
		Payload:        payload,
	}
	return kc.sendMessage(MsgTypeDistributedTagcast, data)
}

// SendNodeJoin 发送节点加入消息
func (kc *KafkaClient) SendNodeJoin(nodeInfo *NodeInfo) error {
	return kc.sendMessage(MsgTypeNodeJoin, nodeInfo)
}

// SendNodeLeave 发送节点离开消息
func (kc *KafkaClient) SendNodeLeave() error {
	return kc.sendMessage(MsgTypeNodeLeave, kc.nodeID)
}

// SendNodeSync 发送节点同步消息
func (kc *KafkaClient) SendNodeSync(nodes []*NodeInfo) error {
	return kc.sendMessage(MsgTypeNodeSync, nodes)
}

// GetNodeID 获取节点ID
func (kc *KafkaClient) GetNodeID() string {
	return kc.nodeID
}

// GetTopic 获取主题名称
func (kc *KafkaClient) GetTopic() string {
	return kc.topic
}

// IsHealthy 检查Kafka客户端健康状态
func (kc *KafkaClient) IsHealthy() bool {
	if kc.producer == nil || kc.consumerGroup == nil {
		return false
	}

	// 尝试发送一个测试消息
	testMsg := &sarama.ProducerMessage{
		Topic: kc.topic,
		Value: sarama.StringEncoder(`{"type":"health_check","node_id":"` + kc.nodeID + `"}`),
	}

	_, _, err := kc.producer.SendMessage(testMsg)
	return err == nil
}
