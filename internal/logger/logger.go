package logger

import (
	"fmt"
	"os"
	"path/filepath"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"

	"websocket-hub/internal/config"
)

// NewLogger 创建日志记录器
func NewLogger(cfg *config.LogConfig) (*zap.Logger, error) {
	// 设置日志级别
	level, err := parseLogLevel(cfg.Level)
	if err != nil {
		return nil, fmt.Errorf("invalid log level: %w", err)
	}

	// 创建编码器配置
	encoderConfig := getEncoderConfig(cfg.Format)

	// 创建编码器
	var encoder zapcore.Encoder
	switch cfg.Format {
	case "json":
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	case "console":
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	default:
		return nil, fmt.Errorf("unsupported log format: %s", cfg.Format)
	}

	// 创建写入器
	writer, err := getWriter(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create log writer: %w", err)
	}

	// 创建核心
	core := zapcore.NewCore(encoder, writer, level)

	// 创建日志记录器
	logger := zap.New(core, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))

	return logger, nil
}

// parseLogLevel 解析日志级别
func parseLogLevel(levelStr string) (zapcore.Level, error) {
	switch levelStr {
	case "debug":
		return zapcore.DebugLevel, nil
	case "info":
		return zapcore.InfoLevel, nil
	case "warn":
		return zapcore.WarnLevel, nil
	case "error":
		return zapcore.ErrorLevel, nil
	case "fatal":
		return zapcore.FatalLevel, nil
	default:
		return zapcore.InfoLevel, fmt.Errorf("unknown log level: %s", levelStr)
	}
}

// getEncoderConfig 获取编码器配置
func getEncoderConfig(format string) zapcore.EncoderConfig {
	config := zap.NewProductionEncoderConfig()
	
	// 设置时间格式
	config.TimeKey = "timestamp"
	config.EncodeTime = zapcore.ISO8601TimeEncoder
	
	// 设置级别格式
	config.LevelKey = "level"
	config.EncodeLevel = zapcore.LowercaseLevelEncoder
	
	// 设置调用者格式
	config.CallerKey = "caller"
	config.EncodeCaller = zapcore.ShortCallerEncoder
	
	// 设置消息键
	config.MessageKey = "message"
	
	// 设置堆栈跟踪键
	config.StacktraceKey = "stacktrace"

	// 根据格式调整配置
	if format == "console" {
		config.EncodeLevel = zapcore.CapitalColorLevelEncoder
		config.EncodeTime = zapcore.TimeEncoderOfLayout("2006-01-02 15:04:05")
	}

	return config
}

// getWriter 获取写入器
func getWriter(cfg *config.LogConfig) (zapcore.WriteSyncer, error) {
	switch cfg.Output {
	case "stdout":
		return zapcore.AddSync(os.Stdout), nil
	case "stderr":
		return zapcore.AddSync(os.Stderr), nil
	default:
		// 文件输出
		if cfg.Output == "" {
			return nil, fmt.Errorf("log output path is empty")
		}

		// 创建日志目录
		logDir := filepath.Dir(cfg.Output)
		if err := os.MkdirAll(logDir, 0755); err != nil {
			return nil, fmt.Errorf("failed to create log directory: %w", err)
		}

		// 使用lumberjack进行日志轮转
		lumberjackLogger := &lumberjack.Logger{
			Filename:   cfg.Output,
			MaxSize:    cfg.MaxSize,    // MB
			MaxBackups: cfg.MaxBackups,
			MaxAge:     cfg.MaxAge,     // days
			Compress:   cfg.Compress,
		}

		return zapcore.AddSync(lumberjackLogger), nil
	}
}

// NewDevelopmentLogger 创建开发环境日志记录器
func NewDevelopmentLogger() (*zap.Logger, error) {
	config := zap.NewDevelopmentConfig()
	config.EncoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
	config.EncoderConfig.EncodeTime = zapcore.TimeEncoderOfLayout("15:04:05")
	
	return config.Build()
}

// NewProductionLogger 创建生产环境日志记录器
func NewProductionLogger() (*zap.Logger, error) {
	config := zap.NewProductionConfig()
	config.EncoderConfig.TimeKey = "timestamp"
	config.EncoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	
	return config.Build()
}

// LoggerMiddleware 日志中间件
type LoggerMiddleware struct {
	logger *zap.Logger
}

// NewLoggerMiddleware 创建日志中间件
func NewLoggerMiddleware(logger *zap.Logger) *LoggerMiddleware {
	return &LoggerMiddleware{logger: logger}
}

// LogRequest 记录请求日志
func (lm *LoggerMiddleware) LogRequest(method, path, clientIP, userAgent string, statusCode int, duration int64) {
	lm.logger.Info("HTTP Request",
		zap.String("method", method),
		zap.String("path", path),
		zap.String("client_ip", clientIP),
		zap.String("user_agent", userAgent),
		zap.Int("status_code", statusCode),
		zap.Int64("duration_ms", duration),
	)
}

// LogWebSocketConnection 记录WebSocket连接日志
func (lm *LoggerMiddleware) LogWebSocketConnection(deviceID, remoteAddr string, tags []string, action string) {
	lm.logger.Info("WebSocket Connection",
		zap.String("device_id", deviceID),
		zap.String("remote_addr", remoteAddr),
		zap.Strings("tags", tags),
		zap.String("action", action),
	)
}

// LogMessage 记录消息日志
func (lm *LoggerMiddleware) LogMessage(messageType, sourceDevice, targetDevice string, tags []string, size int) {
	fields := []zap.Field{
		zap.String("message_type", messageType),
		zap.String("source_device", sourceDevice),
		zap.Int("message_size", size),
	}

	if targetDevice != "" {
		fields = append(fields, zap.String("target_device", targetDevice))
	}

	if len(tags) > 0 {
		fields = append(fields, zap.Strings("tags", tags))
	}

	lm.logger.Info("Message", fields...)
}

// LogError 记录错误日志
func (lm *LoggerMiddleware) LogError(operation string, err error, fields ...zap.Field) {
	allFields := append([]zap.Field{
		zap.String("operation", operation),
		zap.Error(err),
	}, fields...)

	lm.logger.Error("Operation Error", allFields...)
}

// LogKafkaMessage 记录Kafka消息日志
func (lm *LoggerMiddleware) LogKafkaMessage(topic, messageType, nodeID string, action string) {
	lm.logger.Info("Kafka Message",
		zap.String("topic", topic),
		zap.String("message_type", messageType),
		zap.String("node_id", nodeID),
		zap.String("action", action),
	)
}

// LogSystemEvent 记录系统事件日志
func (lm *LoggerMiddleware) LogSystemEvent(event string, fields ...zap.Field) {
	allFields := append([]zap.Field{
		zap.String("event", event),
	}, fields...)

	lm.logger.Info("System Event", allFields...)
}

// GetLogger 获取底层日志记录器
func (lm *LoggerMiddleware) GetLogger() *zap.Logger {
	return lm.logger
}

// Sync 同步日志缓冲区
func (lm *LoggerMiddleware) Sync() error {
	return lm.logger.Sync()
}

// WithFields 添加字段
func (lm *LoggerMiddleware) WithFields(fields ...zap.Field) *zap.Logger {
	return lm.logger.With(fields...)
}

// Debug 记录调试日志
func (lm *LoggerMiddleware) Debug(msg string, fields ...zap.Field) {
	lm.logger.Debug(msg, fields...)
}

// Info 记录信息日志
func (lm *LoggerMiddleware) Info(msg string, fields ...zap.Field) {
	lm.logger.Info(msg, fields...)
}

// Warn 记录警告日志
func (lm *LoggerMiddleware) Warn(msg string, fields ...zap.Field) {
	lm.logger.Warn(msg, fields...)
}

// Error 记录错误日志
func (lm *LoggerMiddleware) Error(msg string, fields ...zap.Field) {
	lm.logger.Error(msg, fields...)
}

// Fatal 记录致命错误日志
func (lm *LoggerMiddleware) Fatal(msg string, fields ...zap.Field) {
	lm.logger.Fatal(msg, fields...)
}
