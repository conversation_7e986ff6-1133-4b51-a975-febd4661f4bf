package protocol

import (
	"bytes"
	"encoding/binary"
	"errors"
	"time"
)

// MessageType 定义消息类型
type MessageType uint8

const (
	// 连接相关消息
	MsgTypeHandshake     MessageType = 0x01 // 握手消息
	MsgTypeHandshakeResp MessageType = 0x02 // 握手响应
	MsgTypeHeartbeat     MessageType = 0x03 // 心跳消息
	MsgTypeDisconnect    MessageType = 0x04 // 断开连接

	// 数据传输消息
	MsgTypeUnicast   MessageType = 0x10 // 单播消息
	MsgTypeBroadcast MessageType = 0x11 // 广播消息
	MsgTypeTagcast   MessageType = 0x12 // 基于标签的组播消息

	// 系统消息
	MsgTypeError MessageType = 0xF0 // 错误消息
	MsgTypeAck   MessageType = 0xF1 // 确认消息
)

// Message 二进制消息结构
// 消息格式: [Header(16字节)] + [Payload(变长)]
// Header: [Magic(2)] + [Version(1)] + [Type(1)] + [Flags(1)] + [Reserved(1)] + [PayloadLen(4)] + [Timestamp(4)] + [Checksum(2)]
type Message struct {
	Header  MessageHeader
	Payload []byte
}

// MessageHeader 消息头结构
type MessageHeader struct {
	Magic       uint16      // 魔数 0x5757 ('WW')
	Version     uint8       // 协议版本
	Type        MessageType // 消息类型
	Flags       uint8       // 标志位
	Reserved    uint8       // 保留字段
	PayloadLen  uint32      // 载荷长度
	Timestamp   uint32      // 时间戳
	Checksum    uint16      // 校验和
}

const (
	MessageMagic   = 0x5757 // 'WW'
	MessageVersion = 0x01
	HeaderSize     = 16
)

// 标志位定义
const (
	FlagCompressed = 1 << 0 // 压缩标志
	FlagEncrypted  = 1 << 1 // 加密标志
	FlagReliable   = 1 << 2 // 可靠传输标志
)

// HandshakePayload 握手消息载荷
type HandshakePayload struct {
	DeviceID   string   // 设备ID
	Tags       []string // 连接标签
	ClientInfo string   // 客户端信息
	JWTToken   string   // JWT认证token
}

// UnicastPayload 单播消息载荷
type UnicastPayload struct {
	TargetDeviceID string // 目标设备ID
	Data           []byte // 消息数据
}

// BroadcastPayload 广播消息载荷
type BroadcastPayload struct {
	Data []byte // 消息数据
}

// TagcastPayload 标签组播消息载荷
type TagcastPayload struct {
	Tags []string // 目标标签列表
	Data []byte   // 消息数据
}

// ErrorPayload 错误消息载荷
type ErrorPayload struct {
	Code    uint32 // 错误码
	Message string // 错误信息
}

// NewMessage 创建新消息
func NewMessage(msgType MessageType, payload []byte) *Message {
	header := MessageHeader{
		Magic:      MessageMagic,
		Version:    MessageVersion,
		Type:       msgType,
		Flags:      0,
		Reserved:   0,
		PayloadLen: uint32(len(payload)),
		Timestamp:  uint32(time.Now().Unix()),
		Checksum:   0, // 稍后计算
	}

	msg := &Message{
		Header:  header,
		Payload: payload,
	}

	// 计算校验和
	msg.Header.Checksum = msg.calculateChecksum()
	return msg
}

// Marshal 序列化消息为二进制数据
func (m *Message) Marshal() ([]byte, error) {
	buf := new(bytes.Buffer)

	// 写入头部
	if err := binary.Write(buf, binary.BigEndian, m.Header); err != nil {
		return nil, err
	}

	// 写入载荷
	if _, err := buf.Write(m.Payload); err != nil {
		return nil, err
	}

	return buf.Bytes(), nil
}

// Unmarshal 从二进制数据反序列化消息
func (m *Message) Unmarshal(data []byte) error {
	if len(data) < HeaderSize {
		return errors.New("data too short for message header")
	}

	buf := bytes.NewReader(data)

	// 读取头部
	if err := binary.Read(buf, binary.BigEndian, &m.Header); err != nil {
		return err
	}

	// 验证魔数和版本
	if m.Header.Magic != MessageMagic {
		return errors.New("invalid message magic")
	}
	if m.Header.Version != MessageVersion {
		return errors.New("unsupported message version")
	}

	// 验证载荷长度
	if uint32(len(data)-HeaderSize) != m.Header.PayloadLen {
		return errors.New("payload length mismatch")
	}

	// 读取载荷
	if m.Header.PayloadLen > 0 {
		m.Payload = make([]byte, m.Header.PayloadLen)
		if _, err := buf.Read(m.Payload); err != nil {
			return err
		}
	}

	// 验证校验和
	checksum := m.calculateChecksum()
	if checksum != m.Header.Checksum {
		return errors.New("checksum verification failed")
	}

	return nil
}

// calculateChecksum 计算消息校验和
func (m *Message) calculateChecksum() uint16 {
	// 简单的校验和算法，实际应用中可以使用CRC16
	var sum uint16
	
	// 计算头部校验和（除了校验和字段本身）
	sum += m.Header.Magic
	sum += uint16(m.Header.Version)
	sum += uint16(m.Header.Type)
	sum += uint16(m.Header.Flags)
	sum += uint16(m.Header.Reserved)
	sum += uint16(m.Header.PayloadLen)
	sum += uint16(m.Header.PayloadLen >> 16)
	sum += uint16(m.Header.Timestamp)
	sum += uint16(m.Header.Timestamp >> 16)

	// 计算载荷校验和
	for _, b := range m.Payload {
		sum += uint16(b)
	}

	return sum
}

// IsValid 验证消息是否有效
func (m *Message) IsValid() bool {
	return m.Header.Magic == MessageMagic &&
		m.Header.Version == MessageVersion &&
		uint32(len(m.Payload)) == m.Header.PayloadLen &&
		m.calculateChecksum() == m.Header.Checksum
}

// GetType 获取消息类型
func (m *Message) GetType() MessageType {
	return m.Header.Type
}

// GetPayload 获取载荷数据
func (m *Message) GetPayload() []byte {
	return m.Payload
}

// SetFlag 设置标志位
func (m *Message) SetFlag(flag uint8) {
	m.Header.Flags |= flag
	m.Header.Checksum = m.calculateChecksum()
}

// HasFlag 检查是否有指定标志位
func (m *Message) HasFlag(flag uint8) bool {
	return m.Header.Flags&flag != 0
}
