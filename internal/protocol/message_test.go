package protocol

import (
	"testing"
)

func TestMessageMarshalUnmarshal(t *testing.T) {
	// 测试数据
	testPayload := []byte("Hello, WebSocket!")
	
	// 创建消息
	originalMsg := NewMessage(MsgTypeUnicast, testPayload)
	
	// 序列化
	data, err := originalMsg.Marshal()
	if err != nil {
		t.Fatalf("Failed to marshal message: %v", err)
	}
	
	// 反序列化
	var newMsg Message
	err = newMsg.Unmarshal(data)
	if err != nil {
		t.Fatalf("Failed to unmarshal message: %v", err)
	}
	
	// 验证
	if newMsg.Header.Magic != MessageMagic {
		t.<PERSON>rf("Expected magic %x, got %x", MessageMagic, newMsg.Header.Magic)
	}
	
	if newMsg.Header.Version != MessageVersion {
		t.Errorf("Expected version %d, got %d", MessageVersion, newMsg.Header.Version)
	}
	
	if newMsg.Header.Type != MsgTypeUnicast {
		t.<PERSON>("Expected type %v, got %v", MsgTypeUnicast, newMsg.Header.Type)
	}
	
	if string(newMsg.Payload) != string(testPayload) {
		t.<PERSON>("Expected payload %s, got %s", string(testPayload), string(newMsg.Payload))
	}
	
	if !newMsg.IsValid() {
		t.Error("Message should be valid")
	}
}

func TestMessageTypes(t *testing.T) {
	testCases := []struct {
		msgType MessageType
		name    string
	}{
		{MsgTypeHandshake, "Handshake"},
		{MsgTypeHandshakeResp, "HandshakeResp"},
		{MsgTypeHeartbeat, "Heartbeat"},
		{MsgTypeDisconnect, "Disconnect"},
		{MsgTypeUnicast, "Unicast"},
		{MsgTypeBroadcast, "Broadcast"},
		{MsgTypeTagcast, "Tagcast"},
		{MsgTypeError, "Error"},
		{MsgTypeAck, "Ack"},
	}
	
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			msg := NewMessage(tc.msgType, []byte("test"))
			
			if msg.GetType() != tc.msgType {
				t.Errorf("Expected type %v, got %v", tc.msgType, msg.GetType())
			}
			
			data, err := msg.Marshal()
			if err != nil {
				t.Fatalf("Failed to marshal %s message: %v", tc.name, err)
			}
			
			var newMsg Message
			err = newMsg.Unmarshal(data)
			if err != nil {
				t.Fatalf("Failed to unmarshal %s message: %v", tc.name, err)
			}
			
			if newMsg.GetType() != tc.msgType {
				t.Errorf("After unmarshal, expected type %v, got %v", tc.msgType, newMsg.GetType())
			}
		})
	}
}

func TestMessageFlags(t *testing.T) {
	msg := NewMessage(MsgTypeUnicast, []byte("test"))
	
	// 测试压缩标志
	msg.SetFlag(FlagCompressed)
	if !msg.HasFlag(FlagCompressed) {
		t.Error("Message should have compressed flag")
	}
	
	// 测试加密标志
	msg.SetFlag(FlagEncrypted)
	if !msg.HasFlag(FlagEncrypted) {
		t.Error("Message should have encrypted flag")
	}
	
	// 测试可靠传输标志
	msg.SetFlag(FlagReliable)
	if !msg.HasFlag(FlagReliable) {
		t.Error("Message should have reliable flag")
	}
	
	// 验证所有标志都设置了
	expectedFlags := uint8(FlagCompressed | FlagEncrypted | FlagReliable)
	if msg.Header.Flags != expectedFlags {
		t.Errorf("Expected flags %d, got %d", expectedFlags, msg.Header.Flags)
	}
}

func TestMessageValidation(t *testing.T) {
	// 测试有效消息
	validMsg := NewMessage(MsgTypeUnicast, []byte("test"))
	if !validMsg.IsValid() {
		t.Error("Valid message should pass validation")
	}
	
	// 测试无效魔数
	invalidMagicMsg := NewMessage(MsgTypeUnicast, []byte("test"))
	invalidMagicMsg.Header.Magic = 0x1234
	if invalidMagicMsg.IsValid() {
		t.Error("Message with invalid magic should fail validation")
	}
	
	// 测试无效版本
	invalidVersionMsg := NewMessage(MsgTypeUnicast, []byte("test"))
	invalidVersionMsg.Header.Version = 0xFF
	if invalidVersionMsg.IsValid() {
		t.Error("Message with invalid version should fail validation")
	}
	
	// 测试载荷长度不匹配
	invalidLengthMsg := NewMessage(MsgTypeUnicast, []byte("test"))
	invalidLengthMsg.Header.PayloadLen = 100
	if invalidLengthMsg.IsValid() {
		t.Error("Message with invalid payload length should fail validation")
	}
}

func TestMessageChecksum(t *testing.T) {
	msg := NewMessage(MsgTypeUnicast, []byte("test"))
	originalChecksum := msg.Header.Checksum
	
	// 修改载荷
	msg.Payload = []byte("modified")
	
	// 校验和应该不匹配
	if msg.IsValid() {
		t.Error("Message with modified payload should fail checksum validation")
	}
	
	// 重新计算校验和
	msg.Header.PayloadLen = uint32(len(msg.Payload))
	msg.Header.Checksum = msg.calculateChecksum()
	
	// 现在应该有效
	if !msg.IsValid() {
		t.Error("Message with recalculated checksum should be valid")
	}
	
	// 校验和应该不同
	if msg.Header.Checksum == originalChecksum {
		t.Error("Checksum should be different after payload modification")
	}
}

func TestEmptyPayload(t *testing.T) {
	// 测试空载荷
	msg := NewMessage(MsgTypeHeartbeat, nil)
	
	if msg.Header.PayloadLen != 0 {
		t.Errorf("Expected payload length 0, got %d", msg.Header.PayloadLen)
	}
	
	if len(msg.Payload) != 0 {
		t.Errorf("Expected empty payload, got length %d", len(msg.Payload))
	}
	
	if !msg.IsValid() {
		t.Error("Message with empty payload should be valid")
	}
	
	// 序列化和反序列化
	data, err := msg.Marshal()
	if err != nil {
		t.Fatalf("Failed to marshal message with empty payload: %v", err)
	}
	
	var newMsg Message
	err = newMsg.Unmarshal(data)
	if err != nil {
		t.Fatalf("Failed to unmarshal message with empty payload: %v", err)
	}
	
	if !newMsg.IsValid() {
		t.Error("Unmarshaled message with empty payload should be valid")
	}
}

func TestLargePayload(t *testing.T) {
	// 创建大载荷（1MB）
	largePayload := make([]byte, 1024*1024)
	for i := range largePayload {
		largePayload[i] = byte(i % 256)
	}
	
	msg := NewMessage(MsgTypeBroadcast, largePayload)
	
	// 序列化
	data, err := msg.Marshal()
	if err != nil {
		t.Fatalf("Failed to marshal large message: %v", err)
	}
	
	// 反序列化
	var newMsg Message
	err = newMsg.Unmarshal(data)
	if err != nil {
		t.Fatalf("Failed to unmarshal large message: %v", err)
	}
	
	// 验证载荷
	if len(newMsg.Payload) != len(largePayload) {
		t.Errorf("Expected payload length %d, got %d", len(largePayload), len(newMsg.Payload))
	}
	
	for i, b := range newMsg.Payload {
		if b != largePayload[i] {
			t.Errorf("Payload mismatch at index %d: expected %d, got %d", i, largePayload[i], b)
			break
		}
	}
}

func BenchmarkMessageMarshal(b *testing.B) {
	payload := make([]byte, 1024) // 1KB payload
	msg := NewMessage(MsgTypeUnicast, payload)
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := msg.Marshal()
		if err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkMessageUnmarshal(b *testing.B) {
	payload := make([]byte, 1024) // 1KB payload
	msg := NewMessage(MsgTypeUnicast, payload)
	data, _ := msg.Marshal()
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var newMsg Message
		err := newMsg.Unmarshal(data)
		if err != nil {
			b.Fatal(err)
		}
	}
}
