package device

import (
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"encoding/binary"
	"encoding/hex"
	"errors"
	"fmt"
	"strings"
	"time"
)

// DeviceIDGenerator 设备ID生成器
type DeviceIDGenerator struct {
	secretKey []byte // 用于签名的密钥
}

// DeviceInfo 设备信息结构
type DeviceInfo struct {
	ID         string    // 设备ID
	Tags       []string  // 设备标签
	CreatedAt  time.Time // 创建时间
	LastSeen   time.Time // 最后活跃时间
	RemoteAddr string    // 远程地址
	UserAgent  string    // 用户代理
}

// NewDeviceIDGenerator 创建设备ID生成器
func NewDeviceIDGenerator(secretKey string) *DeviceIDGenerator {
	if secretKey == "" {
		// 如果没有提供密钥，生成一个随机密钥
		key := make([]byte, 32)
		rand.Read(key)
		return &DeviceIDGenerator{secretKey: key}
	}
	
	// 使用SHA256对密钥进行哈希，确保长度一致
	hash := sha256.Sum256([]byte(secretKey))
	return &DeviceIDGenerator{secretKey: hash[:]}
}

// GenerateDeviceID 生成安全的设备ID
// 格式: WS_<timestamp>_<random>_<signature>
func (g *DeviceIDGenerator) GenerateDeviceID() (string, error) {
	// 1. 生成时间戳（8字节）
	timestamp := time.Now().Unix()
	timestampBytes := make([]byte, 8)
	binary.BigEndian.PutUint64(timestampBytes, uint64(timestamp))

	// 2. 生成随机数（8字节）
	randomBytes := make([]byte, 8)
	if _, err := rand.Read(randomBytes); err != nil {
		return "", fmt.Errorf("failed to generate random bytes: %w", err)
	}

	// 3. 组合数据
	data := append(timestampBytes, randomBytes...)

	// 4. 生成HMAC签名
	mac := hmac.New(sha256.New, g.secretKey)
	mac.Write(data)
	signature := mac.Sum(nil)

	// 5. 组合最终的设备ID
	// 使用十六进制编码避免分隔符冲突
	timestampStr := hex.EncodeToString(timestampBytes)
	randomStr := hex.EncodeToString(randomBytes)
	signatureStr := hex.EncodeToString(signature[:16]) // 只取前16字节

	deviceID := fmt.Sprintf("WS_%s_%s_%s", timestampStr, randomStr, signatureStr)
	return deviceID, nil
}

// ValidateDeviceID 验证设备ID的有效性
func (g *DeviceIDGenerator) ValidateDeviceID(deviceID string) error {
	// 1. 检查格式
	parts := strings.Split(deviceID, "_")
	if len(parts) != 4 || parts[0] != "WS" {
		return errors.New("invalid device ID format")
	}

	timestampStr := parts[1]
	randomStr := parts[2]
	signatureStr := parts[3]

	// 2. 解码各部分
	timestampBytes, err := hex.DecodeString(timestampStr)
	if err != nil {
		return fmt.Errorf("invalid timestamp encoding: %w", err)
	}

	randomBytes, err := hex.DecodeString(randomStr)
	if err != nil {
		return fmt.Errorf("invalid random encoding: %w", err)
	}

	providedSignature, err := hex.DecodeString(signatureStr)
	if err != nil {
		return fmt.Errorf("invalid signature encoding: %w", err)
	}

	// 3. 验证长度
	if len(timestampBytes) != 8 || len(randomBytes) != 8 {
		return errors.New("invalid component lengths")
	}

	// 4. 重新计算签名
	data := append(timestampBytes, randomBytes...)
	mac := hmac.New(sha256.New, g.secretKey)
	mac.Write(data)
	expectedSignature := mac.Sum(nil)[:16]

	// 5. 比较签名
	if !hmac.Equal(providedSignature, expectedSignature) {
		return errors.New("signature verification failed")
	}

	// 6. 检查时间戳是否合理（不能太旧或太新）
	timestamp := int64(binary.BigEndian.Uint64(timestampBytes))
	now := time.Now().Unix()
	
	// 设备ID不能超过24小时前生成
	if now-timestamp > 24*60*60 {
		return errors.New("device ID expired")
	}
	
	// 设备ID不能是未来时间生成（允许5分钟时钟偏差）
	if timestamp-now > 5*60 {
		return errors.New("device ID from future")
	}

	return nil
}

// ExtractTimestamp 从设备ID中提取时间戳
func (g *DeviceIDGenerator) ExtractTimestamp(deviceID string) (time.Time, error) {
	parts := strings.Split(deviceID, "_")
	if len(parts) != 4 || parts[0] != "WS" {
		return time.Time{}, errors.New("invalid device ID format")
	}

	timestampBytes, err := hex.DecodeString(parts[1])
	if err != nil {
		return time.Time{}, fmt.Errorf("invalid timestamp encoding: %w", err)
	}

	if len(timestampBytes) != 8 {
		return time.Time{}, errors.New("invalid timestamp length")
	}

	timestamp := int64(binary.BigEndian.Uint64(timestampBytes))
	return time.Unix(timestamp, 0), nil
}

// IsDeviceIDExpired 检查设备ID是否过期
func (g *DeviceIDGenerator) IsDeviceIDExpired(deviceID string, maxAge time.Duration) bool {
	timestamp, err := g.ExtractTimestamp(deviceID)
	if err != nil {
		return true // 无法解析的ID视为过期
	}

	return time.Since(timestamp) > maxAge
}



// DeviceManager 设备管理器
type DeviceManager struct {
	generator *DeviceIDGenerator
	devices   map[string]*DeviceInfo // deviceID -> DeviceInfo
}

// NewDeviceManager 创建设备管理器
func NewDeviceManager(secretKey string) *DeviceManager {
	return &DeviceManager{
		generator: NewDeviceIDGenerator(secretKey),
		devices:   make(map[string]*DeviceInfo),
	}
}

// RegisterDevice 注册新设备
func (dm *DeviceManager) RegisterDevice(tags []string, remoteAddr, userAgent string) (*DeviceInfo, error) {
	deviceID, err := dm.generator.GenerateDeviceID()
	if err != nil {
		return nil, fmt.Errorf("failed to generate device ID: %w", err)
	}

	device := &DeviceInfo{
		ID:         deviceID,
		Tags:       tags,
		CreatedAt:  time.Now(),
		LastSeen:   time.Now(),
		RemoteAddr: remoteAddr,
		UserAgent:  userAgent,
	}

	dm.devices[deviceID] = device
	return device, nil
}

// GetDevice 获取设备信息
func (dm *DeviceManager) GetDevice(deviceID string) (*DeviceInfo, bool) {
	device, exists := dm.devices[deviceID]
	return device, exists
}

// ValidateDevice 验证设备
func (dm *DeviceManager) ValidateDevice(deviceID string) error {
	return dm.generator.ValidateDeviceID(deviceID)
}

// UpdateLastSeen 更新设备最后活跃时间
func (dm *DeviceManager) UpdateLastSeen(deviceID string) {
	if device, exists := dm.devices[deviceID]; exists {
		device.LastSeen = time.Now()
	}
}

// RemoveDevice 移除设备
func (dm *DeviceManager) RemoveDevice(deviceID string) {
	delete(dm.devices, deviceID)
}

// GetDevicesByTag 根据标签获取设备列表
func (dm *DeviceManager) GetDevicesByTag(tag string) []*DeviceInfo {
	var devices []*DeviceInfo
	for _, device := range dm.devices {
		for _, deviceTag := range device.Tags {
			if deviceTag == tag {
				devices = append(devices, device)
				break
			}
		}
	}
	return devices
}

// GetAllDevices 获取所有设备
func (dm *DeviceManager) GetAllDevices() []*DeviceInfo {
	devices := make([]*DeviceInfo, 0, len(dm.devices))
	for _, device := range dm.devices {
		devices = append(devices, device)
	}
	return devices
}

// CleanupExpiredDevices 清理过期设备
func (dm *DeviceManager) CleanupExpiredDevices(maxAge time.Duration) int {
	var expiredDevices []string
	now := time.Now()

	for deviceID, device := range dm.devices {
		if now.Sub(device.LastSeen) > maxAge {
			expiredDevices = append(expiredDevices, deviceID)
		}
	}

	for _, deviceID := range expiredDevices {
		delete(dm.devices, deviceID)
	}

	return len(expiredDevices)
}
