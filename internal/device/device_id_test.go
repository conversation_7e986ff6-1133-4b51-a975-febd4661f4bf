package device

import (
	"strings"
	"testing"
	"time"
)

func TestDeviceIDGeneration(t *testing.T) {
	generator := NewDeviceIDGenerator("test-secret-key")
	
	// 生成设备ID
	deviceID, err := generator.GenerateDeviceID()
	if err != nil {
		t.Fatalf("Failed to generate device ID: %v", err)
	}
	
	// 验证格式
	if !strings.HasPrefix(deviceID, "WS_") {
		t.<PERSON><PERSON>("Device ID should start with 'WS_', got: %s", deviceID)
	}
	
	parts := strings.Split(deviceID, "_")
	if len(parts) != 4 {
		t.Errorf("Device ID should have 4 parts, got %d: %s", len(parts), deviceID)
	}
	
	// 验证设备ID
	if err := generator.ValidateDeviceID(deviceID); err != nil {
		t.Errorf("Generated device ID should be valid: %v", err)
	}
}

func TestDeviceIDValidation(t *testing.T) {
	generator := NewDeviceIDGenerator("test-secret-key")
	
	// 生成有效的设备ID
	validID, err := generator.GenerateDeviceID()
	if err != nil {
		t.Fatalf("Failed to generate device ID: %v", err)
	}
	
	// 测试有效ID
	if err := generator.ValidateDeviceID(validID); err != nil {
		t.Errorf("Valid device ID should pass validation: %v", err)
	}
	
	// 测试无效格式
	invalidIDs := []string{
		"",
		"invalid",
		"WS_invalid",
		"WS_a_b",
		"WS_a_b_c_d",
		"INVALID_a_b_c",
	}
	
	for _, invalidID := range invalidIDs {
		if err := generator.ValidateDeviceID(invalidID); err == nil {
			t.Errorf("Invalid device ID should fail validation: %s", invalidID)
		}
	}
}

func TestDeviceIDUniqueness(t *testing.T) {
	generator := NewDeviceIDGenerator("test-secret-key")
	
	// 生成多个设备ID
	ids := make(map[string]bool)
	for i := 0; i < 1000; i++ {
		deviceID, err := generator.GenerateDeviceID()
		if err != nil {
			t.Fatalf("Failed to generate device ID: %v", err)
		}
		
		if ids[deviceID] {
			t.Errorf("Duplicate device ID generated: %s", deviceID)
		}
		ids[deviceID] = true
	}
}

func TestDeviceIDTimestamp(t *testing.T) {
	generator := NewDeviceIDGenerator("test-secret-key")
	
	before := time.Now()
	deviceID, err := generator.GenerateDeviceID()
	if err != nil {
		t.Fatalf("Failed to generate device ID: %v", err)
	}
	after := time.Now()
	
	// 提取时间戳
	timestamp, err := generator.ExtractTimestamp(deviceID)
	if err != nil {
		t.Fatalf("Failed to extract timestamp: %v", err)
	}
	
	// 验证时间戳在合理范围内（允许1秒的误差，因为Unix时间戳精度为秒）
	if timestamp.Before(before.Add(-time.Second)) || timestamp.After(after.Add(time.Second)) {
		t.Errorf("Timestamp %v should be between %v and %v (with 1s tolerance)", timestamp, before, after)
	}
}

func TestDeviceIDExpiration(t *testing.T) {
	generator := NewDeviceIDGenerator("test-secret-key")
	
	deviceID, err := generator.GenerateDeviceID()
	if err != nil {
		t.Fatalf("Failed to generate device ID: %v", err)
	}
	
	// 新生成的ID不应该过期
	if generator.IsDeviceIDExpired(deviceID, time.Hour) {
		t.Error("Newly generated device ID should not be expired")
	}
	
	// 使用很短的过期时间
	time.Sleep(time.Millisecond * 10)
	if !generator.IsDeviceIDExpired(deviceID, time.Millisecond*5) {
		t.Error("Device ID should be expired with short max age")
	}
}

func TestDeviceIDWithDifferentKeys(t *testing.T) {
	generator1 := NewDeviceIDGenerator("key1")
	generator2 := NewDeviceIDGenerator("key2")
	
	// 用第一个生成器生成ID
	deviceID, err := generator1.GenerateDeviceID()
	if err != nil {
		t.Fatalf("Failed to generate device ID: %v", err)
	}
	
	// 第一个生成器应该能验证
	if err := generator1.ValidateDeviceID(deviceID); err != nil {
		t.Errorf("Generator1 should validate its own device ID: %v", err)
	}
	
	// 第二个生成器应该无法验证（不同的密钥）
	if err := generator2.ValidateDeviceID(deviceID); err == nil {
		t.Error("Generator2 should not validate device ID from generator1")
	}
}

func TestDeviceManager(t *testing.T) {
	manager := NewDeviceManager("test-secret-key")
	
	// 注册设备
	tags := []string{"tag1", "tag2"}
	device, err := manager.RegisterDevice(tags, "127.0.0.1:12345", "Test Client")
	if err != nil {
		t.Fatalf("Failed to register device: %v", err)
	}
	
	// 验证设备信息
	if device.ID == "" {
		t.Error("Device ID should not be empty")
	}
	
	if len(device.Tags) != len(tags) {
		t.Errorf("Expected %d tags, got %d", len(tags), len(device.Tags))
	}
	
	for i, tag := range device.Tags {
		if tag != tags[i] {
			t.Errorf("Expected tag %s, got %s", tags[i], tag)
		}
	}
	
	// 获取设备
	retrievedDevice, exists := manager.GetDevice(device.ID)
	if !exists {
		t.Error("Device should exist")
	}
	
	if retrievedDevice.ID != device.ID {
		t.Errorf("Expected device ID %s, got %s", device.ID, retrievedDevice.ID)
	}
	
	// 验证设备
	if err := manager.ValidateDevice(device.ID); err != nil {
		t.Errorf("Device should be valid: %v", err)
	}
	
	// 更新最后活跃时间
	oldLastSeen := retrievedDevice.LastSeen
	time.Sleep(time.Millisecond * 10)
	manager.UpdateLastSeen(device.ID)
	
	updatedDevice, _ := manager.GetDevice(device.ID)
	if !updatedDevice.LastSeen.After(oldLastSeen) {
		t.Error("LastSeen should be updated")
	}
	
	// 根据标签获取设备
	devicesWithTag1 := manager.GetDevicesByTag("tag1")
	if len(devicesWithTag1) != 1 {
		t.Errorf("Expected 1 device with tag1, got %d", len(devicesWithTag1))
	}
	
	devicesWithTag3 := manager.GetDevicesByTag("tag3")
	if len(devicesWithTag3) != 0 {
		t.Errorf("Expected 0 devices with tag3, got %d", len(devicesWithTag3))
	}
	
	// 获取所有设备
	allDevices := manager.GetAllDevices()
	if len(allDevices) != 1 {
		t.Errorf("Expected 1 device, got %d", len(allDevices))
	}
	
	// 移除设备
	manager.RemoveDevice(device.ID)
	_, exists = manager.GetDevice(device.ID)
	if exists {
		t.Error("Device should not exist after removal")
	}
}

func TestDeviceManagerCleanup(t *testing.T) {
	manager := NewDeviceManager("test-secret-key")
	
	// 注册多个设备
	device1, _ := manager.RegisterDevice([]string{"tag1"}, "127.0.0.1:1", "Client1")
	device2, _ := manager.RegisterDevice([]string{"tag2"}, "127.0.0.1:2", "Client2")
	device3, _ := manager.RegisterDevice([]string{"tag3"}, "127.0.0.1:3", "Client3")
	
	// 模拟一些设备长时间未活跃
	time.Sleep(time.Millisecond * 10)
	manager.UpdateLastSeen(device2.ID) // 只更新device2
	
	// 清理过期设备
	cleaned := manager.CleanupExpiredDevices(time.Millisecond * 5)
	
	// 应该清理了2个设备（device1和device3）
	if cleaned != 2 {
		t.Errorf("Expected 2 cleaned devices, got %d", cleaned)
	}
	
	// device2应该还存在
	_, exists := manager.GetDevice(device2.ID)
	if !exists {
		t.Error("Device2 should still exist")
	}
	
	// device1和device3应该不存在
	_, exists = manager.GetDevice(device1.ID)
	if exists {
		t.Error("Device1 should be cleaned up")
	}
	
	_, exists = manager.GetDevice(device3.ID)
	if exists {
		t.Error("Device3 should be cleaned up")
	}
}

func BenchmarkDeviceIDGeneration(b *testing.B) {
	generator := NewDeviceIDGenerator("test-secret-key")
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := generator.GenerateDeviceID()
		if err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkDeviceIDValidation(b *testing.B) {
	generator := NewDeviceIDGenerator("test-secret-key")
	deviceID, _ := generator.GenerateDeviceID()
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		err := generator.ValidateDeviceID(deviceID)
		if err != nil {
			b.Fatal(err)
		}
	}
}
