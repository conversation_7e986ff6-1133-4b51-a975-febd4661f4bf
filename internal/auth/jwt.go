package auth

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"
)

// JWTClaims JWT声明结构
type JWTClaims struct {
	// 标准声明
	Issuer    string `json:"iss,omitempty"` // 签发者
	Subject   string `json:"sub,omitempty"` // 主题
	Audience  string `json:"aud,omitempty"` // 受众
	ExpiresAt int64  `json:"exp,omitempty"` // 过期时间
	NotBefore int64  `json:"nbf,omitempty"` // 生效时间
	IssuedAt  int64  `json:"iat,omitempty"` // 签发时间
	JWTID     string `json:"jti,omitempty"` // JWT ID

	// 自定义声明
	DeviceID  string   `json:"device_id,omitempty"`  // 设备ID
	Tags      []string `json:"tags,omitempty"`       // 设备标签
	Timestamp int64    `json:"timestamp"`            // 当前时间戳（用于验证）
}

// JWTHeader JWT头部结构
type JWTHeader struct {
	Algorithm string `json:"alg"` // 算法
	Type      string `json:"typ"` // 类型
}

// JWTManager JWT管理器
type JWTManager struct {
	secretKey      []byte        // 签名密钥
	issuer         string        // 签发者
	defaultExpiry  time.Duration // 默认过期时间
	maxClockSkew   time.Duration // 最大时钟偏差
	timestampValid time.Duration // 时间戳有效期
}

// NewJWTManager 创建JWT管理器
func NewJWTManager(secretKey, issuer string, defaultExpiry, maxClockSkew, timestampValid time.Duration) *JWTManager {
	return &JWTManager{
		secretKey:      []byte(secretKey),
		issuer:         issuer,
		defaultExpiry:  defaultExpiry,
		maxClockSkew:   maxClockSkew,
		timestampValid: timestampValid,
	}
}

// GenerateToken 生成JWT token
func (jm *JWTManager) GenerateToken(deviceID string, tags []string, customExpiry ...time.Duration) (string, error) {
	now := time.Now()
	expiry := jm.defaultExpiry
	if len(customExpiry) > 0 {
		expiry = customExpiry[0]
	}

	// 创建声明
	claims := JWTClaims{
		Issuer:    jm.issuer,
		Subject:   deviceID,
		ExpiresAt: now.Add(expiry).Unix(),
		NotBefore: now.Unix(),
		IssuedAt:  now.Unix(),
		DeviceID:  deviceID,
		Tags:      tags,
		Timestamp: now.Unix(),
	}

	// 创建头部
	header := JWTHeader{
		Algorithm: "HS256",
		Type:      "JWT",
	}

	// 编码头部
	headerJSON, err := json.Marshal(header)
	if err != nil {
		return "", fmt.Errorf("failed to marshal header: %w", err)
	}
	headerEncoded := base64.RawURLEncoding.EncodeToString(headerJSON)

	// 编码载荷
	claimsJSON, err := json.Marshal(claims)
	if err != nil {
		return "", fmt.Errorf("failed to marshal claims: %w", err)
	}
	claimsEncoded := base64.RawURLEncoding.EncodeToString(claimsJSON)

	// 创建签名
	message := headerEncoded + "." + claimsEncoded
	signature := jm.sign(message)
	signatureEncoded := base64.RawURLEncoding.EncodeToString(signature)

	// 组合token
	token := message + "." + signatureEncoded
	return token, nil
}

// ValidateToken 验证JWT token
func (jm *JWTManager) ValidateToken(tokenString string) (*JWTClaims, error) {
	// 分割token
	parts := strings.Split(tokenString, ".")
	if len(parts) != 3 {
		return nil, errors.New("invalid token format")
	}

	headerEncoded := parts[0]
	claimsEncoded := parts[1]
	signatureEncoded := parts[2]

	// 验证签名
	message := headerEncoded + "." + claimsEncoded
	expectedSignature := jm.sign(message)
	providedSignature, err := base64.RawURLEncoding.DecodeString(signatureEncoded)
	if err != nil {
		return nil, fmt.Errorf("failed to decode signature: %w", err)
	}

	if !hmac.Equal(expectedSignature, providedSignature) {
		return nil, errors.New("invalid signature")
	}

	// 解码头部
	headerJSON, err := base64.RawURLEncoding.DecodeString(headerEncoded)
	if err != nil {
		return nil, fmt.Errorf("failed to decode header: %w", err)
	}

	var header JWTHeader
	if err := json.Unmarshal(headerJSON, &header); err != nil {
		return nil, fmt.Errorf("failed to unmarshal header: %w", err)
	}

	// 验证算法
	if header.Algorithm != "HS256" {
		return nil, errors.New("unsupported algorithm")
	}

	// 解码声明
	claimsJSON, err := base64.RawURLEncoding.DecodeString(claimsEncoded)
	if err != nil {
		return nil, fmt.Errorf("failed to decode claims: %w", err)
	}

	var claims JWTClaims
	if err := json.Unmarshal(claimsJSON, &claims); err != nil {
		return nil, fmt.Errorf("failed to unmarshal claims: %w", err)
	}

	// 验证时间
	now := time.Now()
	
	// 检查过期时间
	if claims.ExpiresAt > 0 && now.Unix() > claims.ExpiresAt+int64(jm.maxClockSkew.Seconds()) {
		return nil, errors.New("token expired")
	}

	// 检查生效时间
	if claims.NotBefore > 0 && now.Unix() < claims.NotBefore-int64(jm.maxClockSkew.Seconds()) {
		return nil, errors.New("token not yet valid")
	}

	// 检查时间戳有效性
	if claims.Timestamp > 0 {
		tokenTime := time.Unix(claims.Timestamp, 0)
		if now.Sub(tokenTime) > jm.timestampValid {
			return nil, errors.New("token timestamp too old")
		}
		if tokenTime.Sub(now) > jm.maxClockSkew {
			return nil, errors.New("token timestamp from future")
		}
	}

	return &claims, nil
}

// sign 使用HMAC-SHA256签名
func (jm *JWTManager) sign(message string) []byte {
	mac := hmac.New(sha256.New, jm.secretKey)
	mac.Write([]byte(message))
	return mac.Sum(nil)
}

// ExtractClaimsWithoutValidation 不验证直接提取声明（用于调试）
func (jm *JWTManager) ExtractClaimsWithoutValidation(tokenString string) (*JWTClaims, error) {
	parts := strings.Split(tokenString, ".")
	if len(parts) != 3 {
		return nil, errors.New("invalid token format")
	}

	claimsJSON, err := base64.RawURLEncoding.DecodeString(parts[1])
	if err != nil {
		return nil, fmt.Errorf("failed to decode claims: %w", err)
	}

	var claims JWTClaims
	if err := json.Unmarshal(claimsJSON, &claims); err != nil {
		return nil, fmt.Errorf("failed to unmarshal claims: %w", err)
	}

	return &claims, nil
}

// IsTokenExpired 检查token是否过期
func (jm *JWTManager) IsTokenExpired(tokenString string) bool {
	claims, err := jm.ExtractClaimsWithoutValidation(tokenString)
	if err != nil {
		return true
	}

	if claims.ExpiresAt == 0 {
		return false // 没有过期时间设置
	}

	return time.Now().Unix() > claims.ExpiresAt
}

// GetTokenRemainingTime 获取token剩余有效时间
func (jm *JWTManager) GetTokenRemainingTime(tokenString string) (time.Duration, error) {
	claims, err := jm.ExtractClaimsWithoutValidation(tokenString)
	if err != nil {
		return 0, err
	}

	if claims.ExpiresAt == 0 {
		return time.Duration(0), errors.New("no expiration time set")
	}

	remaining := time.Unix(claims.ExpiresAt, 0).Sub(time.Now())
	if remaining < 0 {
		return 0, nil
	}

	return remaining, nil
}
