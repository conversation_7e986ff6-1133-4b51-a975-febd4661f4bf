package auth

import (
	"testing"
	"time"
)

func TestJWTManager_GenerateToken(t *testing.T) {
	secretKey := "test-secret-key"
	issuer := "test-issuer"
	defaultExpiry := time.Hour
	maxClockSkew := 5 * time.Minute
	timestampValid := 10 * time.Minute

	jwtManager := NewJWTManager(secretKey, issuer, defaultExpiry, maxClockSkew, timestampValid)

	deviceID := "test-device-123"
	tags := []string{"user", "mobile"}

	token, err := jwtManager.GenerateToken(deviceID, tags)
	if err != nil {
		t.Fatalf("Failed to generate token: %v", err)
	}

	if token == "" {
		t.Fatal("Generated token is empty")
	}

	t.Logf("Generated token: %s", token)
}

func TestJWTManager_ValidateToken(t *testing.T) {
	secretKey := "test-secret-key"
	issuer := "test-issuer"
	defaultExpiry := time.Hour
	maxClockSkew := 5 * time.Minute
	timestampValid := 10 * time.Minute

	jwtManager := NewJWTManager(secretKey, issuer, defaultExpiry, maxClockSkew, timestampValid)

	deviceID := "test-device-123"
	tags := []string{"user", "mobile"}

	// 生成token
	token, err := jwtManager.GenerateToken(deviceID, tags)
	if err != nil {
		t.Fatalf("Failed to generate token: %v", err)
	}

	// 验证token
	claims, err := jwtManager.ValidateToken(token)
	if err != nil {
		t.Fatalf("Failed to validate token: %v", err)
	}

	// 验证声明
	if claims.DeviceID != deviceID {
		t.Errorf("Expected device ID %s, got %s", deviceID, claims.DeviceID)
	}

	if len(claims.Tags) != len(tags) {
		t.Errorf("Expected %d tags, got %d", len(tags), len(claims.Tags))
	}

	for i, tag := range tags {
		if claims.Tags[i] != tag {
			t.Errorf("Expected tag %s, got %s", tag, claims.Tags[i])
		}
	}

	if claims.Issuer != issuer {
		t.Errorf("Expected issuer %s, got %s", issuer, claims.Issuer)
	}

	if claims.Subject != deviceID {
		t.Errorf("Expected subject %s, got %s", deviceID, claims.Subject)
	}
}

func TestJWTManager_ValidateToken_InvalidSignature(t *testing.T) {
	secretKey := "test-secret-key"
	issuer := "test-issuer"
	defaultExpiry := time.Hour
	maxClockSkew := 5 * time.Minute
	timestampValid := 10 * time.Minute

	jwtManager := NewJWTManager(secretKey, issuer, defaultExpiry, maxClockSkew, timestampValid)

	deviceID := "test-device-123"
	tags := []string{"user", "mobile"}

	// 生成token
	token, err := jwtManager.GenerateToken(deviceID, tags)
	if err != nil {
		t.Fatalf("Failed to generate token: %v", err)
	}

	// 修改token使签名无效
	invalidToken := token[:len(token)-5] + "xxxxx"

	// 验证应该失败
	_, err = jwtManager.ValidateToken(invalidToken)
	if err == nil {
		t.Fatal("Expected validation to fail for invalid signature")
	}

	t.Logf("Expected error: %v", err)
}

func TestJWTManager_ValidateToken_ExpiredToken(t *testing.T) {
	secretKey := "test-secret-key"
	issuer := "test-issuer"
	defaultExpiry := time.Millisecond // 很短的过期时间
	maxClockSkew := 5 * time.Minute
	timestampValid := 10 * time.Minute

	jwtManager := NewJWTManager(secretKey, issuer, defaultExpiry, maxClockSkew, timestampValid)

	deviceID := "test-device-123"
	tags := []string{"user", "mobile"}

	// 生成token
	token, err := jwtManager.GenerateToken(deviceID, tags)
	if err != nil {
		t.Fatalf("Failed to generate token: %v", err)
	}

	// 等待token过期
	time.Sleep(10 * time.Millisecond)

	// 验证应该失败
	_, err = jwtManager.ValidateToken(token)
	if err == nil {
		t.Fatal("Expected validation to fail for expired token")
	}

	t.Logf("Expected error: %v", err)
}

func TestJWTManager_ValidateToken_OldTimestamp(t *testing.T) {
	secretKey := "test-secret-key"
	issuer := "test-issuer"
	defaultExpiry := time.Hour
	maxClockSkew := 5 * time.Minute
	timestampValid := time.Millisecond // 很短的时间戳有效期

	jwtManager := NewJWTManager(secretKey, issuer, defaultExpiry, maxClockSkew, timestampValid)

	deviceID := "test-device-123"
	tags := []string{"user", "mobile"}

	// 生成token
	token, err := jwtManager.GenerateToken(deviceID, tags)
	if err != nil {
		t.Fatalf("Failed to generate token: %v", err)
	}

	// 等待时间戳过期
	time.Sleep(10 * time.Millisecond)

	// 验证应该失败
	_, err = jwtManager.ValidateToken(token)
	if err == nil {
		t.Fatal("Expected validation to fail for old timestamp")
	}

	t.Logf("Expected error: %v", err)
}

func TestJWTManager_IsTokenExpired(t *testing.T) {
	secretKey := "test-secret-key"
	issuer := "test-issuer"
	defaultExpiry := time.Hour
	maxClockSkew := 5 * time.Minute
	timestampValid := 10 * time.Minute

	jwtManager := NewJWTManager(secretKey, issuer, defaultExpiry, maxClockSkew, timestampValid)

	deviceID := "test-device-123"
	tags := []string{"user", "mobile"}

	// 生成未过期的token
	token, err := jwtManager.GenerateToken(deviceID, tags)
	if err != nil {
		t.Fatalf("Failed to generate token: %v", err)
	}

	if jwtManager.IsTokenExpired(token) {
		t.Error("Token should not be expired")
	}

	// 生成已过期的token
	expiredToken, err := jwtManager.GenerateToken(deviceID, tags, -time.Hour) // 负数表示已过期
	if err != nil {
		t.Fatalf("Failed to generate expired token: %v", err)
	}

	if !jwtManager.IsTokenExpired(expiredToken) {
		t.Error("Token should be expired")
	}
}

func TestJWTManager_GetTokenRemainingTime(t *testing.T) {
	secretKey := "test-secret-key"
	issuer := "test-issuer"
	defaultExpiry := time.Hour
	maxClockSkew := 5 * time.Minute
	timestampValid := 10 * time.Minute

	jwtManager := NewJWTManager(secretKey, issuer, defaultExpiry, maxClockSkew, timestampValid)

	deviceID := "test-device-123"
	tags := []string{"user", "mobile"}

	// 生成token
	token, err := jwtManager.GenerateToken(deviceID, tags)
	if err != nil {
		t.Fatalf("Failed to generate token: %v", err)
	}

	remaining, err := jwtManager.GetTokenRemainingTime(token)
	if err != nil {
		t.Fatalf("Failed to get remaining time: %v", err)
	}

	// 剩余时间应该接近1小时
	if remaining < 59*time.Minute || remaining > time.Hour {
		t.Errorf("Expected remaining time around 1 hour, got %v", remaining)
	}

	t.Logf("Remaining time: %v", remaining)
}

func TestJWTManager_ExtractClaimsWithoutValidation(t *testing.T) {
	secretKey := "test-secret-key"
	issuer := "test-issuer"
	defaultExpiry := time.Hour
	maxClockSkew := 5 * time.Minute
	timestampValid := 10 * time.Minute

	jwtManager := NewJWTManager(secretKey, issuer, defaultExpiry, maxClockSkew, timestampValid)

	deviceID := "test-device-123"
	tags := []string{"user", "mobile"}

	// 生成token
	token, err := jwtManager.GenerateToken(deviceID, tags)
	if err != nil {
		t.Fatalf("Failed to generate token: %v", err)
	}

	// 提取声明（不验证）
	claims, err := jwtManager.ExtractClaimsWithoutValidation(token)
	if err != nil {
		t.Fatalf("Failed to extract claims: %v", err)
	}

	// 验证声明
	if claims.DeviceID != deviceID {
		t.Errorf("Expected device ID %s, got %s", deviceID, claims.DeviceID)
	}

	if len(claims.Tags) != len(tags) {
		t.Errorf("Expected %d tags, got %d", len(tags), len(claims.Tags))
	}
}

func TestJWTManager_CustomExpiry(t *testing.T) {
	secretKey := "test-secret-key"
	issuer := "test-issuer"
	defaultExpiry := time.Hour
	maxClockSkew := 5 * time.Minute
	timestampValid := 10 * time.Minute

	jwtManager := NewJWTManager(secretKey, issuer, defaultExpiry, maxClockSkew, timestampValid)

	deviceID := "test-device-123"
	tags := []string{"user", "mobile"}
	customExpiry := 30 * time.Minute

	// 生成带自定义过期时间的token
	token, err := jwtManager.GenerateToken(deviceID, tags, customExpiry)
	if err != nil {
		t.Fatalf("Failed to generate token: %v", err)
	}

	// 验证token
	claims, err := jwtManager.ValidateToken(token)
	if err != nil {
		t.Fatalf("Failed to validate token: %v", err)
	}

	// 检查过期时间
	now := time.Now().Unix()
	expectedExpiry := now + int64(customExpiry.Seconds())
	
	// 允许一些时间偏差
	if claims.ExpiresAt < expectedExpiry-5 || claims.ExpiresAt > expectedExpiry+5 {
		t.Errorf("Expected expiry around %d, got %d", expectedExpiry, claims.ExpiresAt)
	}
}
