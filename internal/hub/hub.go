package hub

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"

	"websocket-hub/internal/auth"
	"websocket-hub/internal/connection"
	"websocket-hub/internal/device"
	"websocket-hub/internal/kafka"
)

// Hub WebSocket服务中心
type Hub struct {
	nodeID            string
	connectionManager *connection.ConnectionManager
	deviceManager     *device.DeviceManager
	jwtManager        *auth.JWTManager
	kafkaClient       *kafka.KafkaClient
	logger            *zap.Logger

	// 分布式节点管理
	nodes     map[string]*kafka.NodeInfo // nodeID -> NodeInfo
	nodesMux  sync.RWMutex

	// 统计信息
	stats     *HubStats
	statsMux  sync.RWMutex

	ctx    context.Context
	cancel context.CancelFunc
}

// HubStats 服务中心统计信息
type HubStats struct {
	StartTime           time.Time `json:"start_time"`
	TotalConnections    int64     `json:"total_connections"`
	CurrentConnections  int       `json:"current_connections"`
	MessagesReceived    int64     `json:"messages_received"`
	MessagesSent        int64     `json:"messages_sent"`
	DistributedMessages int64     `json:"distributed_messages"`
	Errors              int64     `json:"errors"`
}

// NewHub 创建WebSocket服务中心
func NewHub(nodeID string, deviceManager *device.DeviceManager, jwtManager *auth.JWTManager, logger *zap.Logger,
	readTimeout, writeTimeout, pingInterval time.Duration, maxMessageSize int64, jwtEnabled bool) *Hub {
	ctx, cancel := context.WithCancel(context.Background())

	hub := &Hub{
		nodeID:        nodeID,
		deviceManager: deviceManager,
		jwtManager:    jwtManager,
		logger:        logger,
		nodes:         make(map[string]*kafka.NodeInfo),
		stats: &HubStats{
			StartTime: time.Now(),
		},
		ctx:    ctx,
		cancel: cancel,
	}

	// 创建连接管理器
	hub.connectionManager = connection.NewConnectionManager(deviceManager, jwtManager, logger,
		readTimeout, writeTimeout, pingInterval, maxMessageSize, jwtEnabled)

	return hub
}

// SetKafkaClient 设置Kafka客户端
func (h *Hub) SetKafkaClient(kafkaClient *kafka.KafkaClient) {
	h.kafkaClient = kafkaClient
}

// Start 启动服务中心
func (h *Hub) Start() error {
	// 启动Kafka客户端
	if h.kafkaClient != nil {
		if err := h.kafkaClient.Start(); err != nil {
			return fmt.Errorf("failed to start kafka client: %w", err)
		}
	}

	// 启动统计信息更新协程
	go h.updateStats()

	h.logger.Info("Hub started", zap.String("node_id", h.nodeID))
	return nil
}

// Stop 停止服务中心
func (h *Hub) Stop() error {
	h.cancel()

	// 停止Kafka客户端
	if h.kafkaClient != nil {
		if err := h.kafkaClient.Stop(); err != nil {
			h.logger.Error("Failed to stop kafka client", zap.Error(err))
		}
	}

	// 关闭所有连接
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()
	
	if err := h.connectionManager.Shutdown(shutdownCtx); err != nil {
		h.logger.Error("Failed to shutdown connection manager", zap.Error(err))
	}

	h.logger.Info("Hub stopped")
	return nil
}

// GetConnectionManager 获取连接管理器
func (h *Hub) GetConnectionManager() *connection.ConnectionManager {
	return h.connectionManager
}

// GetStats 获取统计信息
func (h *Hub) GetStats() *HubStats {
	h.statsMux.RLock()
	defer h.statsMux.RUnlock()
	
	// 复制统计信息
	stats := *h.stats
	stats.CurrentConnections = h.connectionManager.GetConnectionCount()
	return &stats
}

// updateStats 更新统计信息
func (h *Hub) updateStats() {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			h.statsMux.Lock()
			h.stats.CurrentConnections = h.connectionManager.GetConnectionCount()
			h.statsMux.Unlock()

		case <-h.ctx.Done():
			return
		}
	}
}

// incrementStat 增加统计计数
func (h *Hub) incrementStat(statType string) {
	h.statsMux.Lock()
	defer h.statsMux.Unlock()

	switch statType {
	case "connections":
		h.stats.TotalConnections++
	case "messages_received":
		h.stats.MessagesReceived++
	case "messages_sent":
		h.stats.MessagesSent++
	case "distributed_messages":
		h.stats.DistributedMessages++
	case "errors":
		h.stats.Errors++
	}
}

// 实现kafka.MessageHandler接口

// HandleDistributedUnicast 处理分布式单播消息
func (h *Hub) HandleDistributedUnicast(data *kafka.DistributedUnicastData) error {
	h.incrementStat("distributed_messages")
	
	// 检查目标设备是否在本节点
	if _, exists := h.connectionManager.GetConnectionInfo(data.TargetDeviceID); exists {
		// 目标设备在本节点，直接发送
		if err := h.connectionManager.SendToDevice(data.TargetDeviceID, data.Payload); err != nil {
			h.incrementStat("errors")
			return fmt.Errorf("failed to send to local device: %w", err)
		}
		h.incrementStat("messages_sent")
	}
	// 如果目标设备不在本节点，忽略（其他节点会处理）
	
	return nil
}

// HandleDistributedBroadcast 处理分布式广播消息
func (h *Hub) HandleDistributedBroadcast(data *kafka.DistributedBroadcastData) error {
	h.incrementStat("distributed_messages")
	
	// 广播到本节点的所有连接（除了发送者）
	sent := h.connectionManager.Broadcast(data.Payload)
	h.statsMux.Lock()
	h.stats.MessagesSent += int64(sent)
	h.statsMux.Unlock()
	
	return nil
}

// HandleDistributedTagcast 处理分布式标签组播消息
func (h *Hub) HandleDistributedTagcast(data *kafka.DistributedTagcastData) error {
	h.incrementStat("distributed_messages")
	
	// 根据标签发送到本节点的相关连接
	totalSent := 0
	for _, tag := range data.Tags {
		sent := h.connectionManager.SendToTag(tag, data.Payload)
		totalSent += sent
	}
	
	h.statsMux.Lock()
	h.stats.MessagesSent += int64(totalSent)
	h.statsMux.Unlock()
	
	return nil
}

// HandleNodeJoin 处理节点加入
func (h *Hub) HandleNodeJoin(nodeInfo *kafka.NodeInfo) error {
	h.nodesMux.Lock()
	h.nodes[nodeInfo.NodeID] = nodeInfo
	h.nodesMux.Unlock()
	
	h.logger.Info("Node joined",
		zap.String("node_id", nodeInfo.NodeID),
		zap.String("address", nodeInfo.Address),
		zap.Int("port", nodeInfo.Port),
	)
	
	return nil
}

// HandleNodeLeave 处理节点离开
func (h *Hub) HandleNodeLeave(nodeID string) error {
	h.nodesMux.Lock()
	delete(h.nodes, nodeID)
	h.nodesMux.Unlock()
	
	h.logger.Info("Node left", zap.String("node_id", nodeID))
	return nil
}

// HandleNodeSync 处理节点同步
func (h *Hub) HandleNodeSync(nodes []*kafka.NodeInfo) error {
	h.nodesMux.Lock()
	// 清空现有节点信息
	h.nodes = make(map[string]*kafka.NodeInfo)
	// 添加同步的节点信息
	for _, node := range nodes {
		if node.NodeID != h.nodeID { // 不包括自己
			h.nodes[node.NodeID] = node
		}
	}
	h.nodesMux.Unlock()
	
	h.logger.Info("Nodes synchronized", zap.Int("node_count", len(nodes)))
	return nil
}

// GetNodes 获取所有节点信息
func (h *Hub) GetNodes() []*kafka.NodeInfo {
	h.nodesMux.RLock()
	defer h.nodesMux.RUnlock()
	
	nodes := make([]*kafka.NodeInfo, 0, len(h.nodes))
	for _, node := range h.nodes {
		nodes = append(nodes, node)
	}
	return nodes
}

// SendDistributedMessage 发送分布式消息
func (h *Hub) SendDistributedMessage(msgType string, sourceDeviceID string, data interface{}) error {
	if h.kafkaClient == nil {
		return fmt.Errorf("kafka client not available")
	}

	switch msgType {
	case "unicast":
		if unicastData, ok := data.(*kafka.DistributedUnicastData); ok {
			return h.kafkaClient.SendDistributedUnicast(
				unicastData.TargetDeviceID,
				sourceDeviceID,
				unicastData.Payload,
			)
		}
	case "broadcast":
		if broadcastData, ok := data.(*kafka.DistributedBroadcastData); ok {
			return h.kafkaClient.SendDistributedBroadcast(
				sourceDeviceID,
				broadcastData.Payload,
			)
		}
	case "tagcast":
		if tagcastData, ok := data.(*kafka.DistributedTagcastData); ok {
			return h.kafkaClient.SendDistributedTagcast(
				tagcastData.Tags,
				sourceDeviceID,
				tagcastData.Payload,
			)
		}
	}

	return fmt.Errorf("unsupported message type or invalid data")
}

// IsHealthy 检查服务中心健康状态
func (h *Hub) IsHealthy() bool {
	// 检查Kafka客户端健康状态
	if h.kafkaClient != nil && !h.kafkaClient.IsHealthy() {
		return false
	}
	
	// 检查连接管理器状态
	if h.connectionManager == nil {
		return false
	}
	
	return true
}

// GetNodeID 获取节点ID
func (h *Hub) GetNodeID() string {
	return h.nodeID
}
