package connection

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/gorilla/websocket"
	"go.uber.org/zap"

	"websocket-hub/internal/auth"
	"websocket-hub/internal/device"
	"websocket-hub/internal/protocol"
)

func TestConnectionManager_JWT_Integration(t *testing.T) {
	// 创建测试用的JWT管理器
	secretKey := "test-secret-key"
	issuer := "test-issuer"
	jwtManager := auth.NewJWTManager(
		secretKey,
		issuer,
		time.Hour,
		5*time.Minute,
		10*time.Minute,
	)

	// 创建设备管理器
	deviceManager := device.NewDeviceManager("device-secret")

	// 创建连接管理器（启用JWT）
	logger, _ := zap.NewDevelopment()
	cm := NewConnectionManager(
		deviceManager,
		jwtManager,
		logger,
		60*time.Second,
		10*time.Second,
		30*time.Second,
		1024*1024,
		true, // 启用JWT
	)

	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		cm.HandleWebSocket(w, r)
	}))
	defer server.Close()

	// 将http://转换为ws://
	wsURL := "ws" + strings.TrimPrefix(server.URL, "http")

	t.Run("Valid JWT Token", func(t *testing.T) {
		// 生成有效的JWT token
		deviceID := "test-device-123"
		tags := []string{"user", "mobile"}
		token, err := jwtManager.GenerateToken(deviceID, tags)
		if err != nil {
			t.Fatalf("Failed to generate token: %v", err)
		}

		// 连接WebSocket
		conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
		if err != nil {
			t.Fatalf("Failed to connect: %v", err)
		}
		defer conn.Close()

		// 发送握手消息
		handshake := protocol.HandshakePayload{
			DeviceID:   deviceID,
			Tags:       tags,
			ClientInfo: "test-client",
			JWTToken:   token,
		}

		handshakeJSON, _ := json.Marshal(handshake)
		handshakeMsg := protocol.NewMessage(protocol.MsgTypeHandshake, handshakeJSON)
		handshakeData, _ := handshakeMsg.Marshal()

		err = conn.WriteMessage(websocket.BinaryMessage, handshakeData)
		if err != nil {
			t.Fatalf("Failed to send handshake: %v", err)
		}

		// 读取握手响应
		_, responseData, err := conn.ReadMessage()
		if err != nil {
			t.Fatalf("Failed to read handshake response: %v", err)
		}

		var responseMsg protocol.Message
		err = responseMsg.Unmarshal(responseData)
		if err != nil {
			t.Fatalf("Failed to unmarshal response: %v", err)
		}

		if responseMsg.GetType() != protocol.MsgTypeHandshakeResp {
			t.Fatalf("Expected handshake response, got %v", responseMsg.GetType())
		}

		var response map[string]interface{}
		err = json.Unmarshal(responseMsg.GetPayload(), &response)
		if err != nil {
			t.Fatalf("Failed to unmarshal response payload: %v", err)
		}

		if response["status"] != "connected" {
			t.Errorf("Expected status 'connected', got %v", response["status"])
		}

		if response["device_id"] != deviceID {
			t.Errorf("Expected device_id %s, got %v", deviceID, response["device_id"])
		}
	})

	t.Run("Invalid JWT Token", func(t *testing.T) {
		// 连接WebSocket
		conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
		if err != nil {
			t.Fatalf("Failed to connect: %v", err)
		}
		defer conn.Close()

		// 发送带无效token的握手消息
		handshake := protocol.HandshakePayload{
			DeviceID:   "test-device-456",
			Tags:       []string{"user"},
			ClientInfo: "test-client",
			JWTToken:   "invalid.jwt.token",
		}

		handshakeJSON, _ := json.Marshal(handshake)
		handshakeMsg := protocol.NewMessage(protocol.MsgTypeHandshake, handshakeJSON)
		handshakeData, _ := handshakeMsg.Marshal()

		err = conn.WriteMessage(websocket.BinaryMessage, handshakeData)
		if err != nil {
			t.Fatalf("Failed to send handshake: %v", err)
		}

		// 连接应该被关闭
		conn.SetReadDeadline(time.Now().Add(5 * time.Second))
		_, _, err = conn.ReadMessage()
		if err == nil {
			t.Fatal("Expected connection to be closed due to invalid JWT")
		}
	})

	t.Run("Missing JWT Token", func(t *testing.T) {
		// 连接WebSocket
		conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
		if err != nil {
			t.Fatalf("Failed to connect: %v", err)
		}
		defer conn.Close()

		// 发送不带token的握手消息
		handshake := protocol.HandshakePayload{
			DeviceID:   "test-device-789",
			Tags:       []string{"user"},
			ClientInfo: "test-client",
			// JWTToken 为空
		}

		handshakeJSON, _ := json.Marshal(handshake)
		handshakeMsg := protocol.NewMessage(protocol.MsgTypeHandshake, handshakeJSON)
		handshakeData, _ := handshakeMsg.Marshal()

		err = conn.WriteMessage(websocket.BinaryMessage, handshakeData)
		if err != nil {
			t.Fatalf("Failed to send handshake: %v", err)
		}

		// 连接应该被关闭
		conn.SetReadDeadline(time.Now().Add(5 * time.Second))
		_, _, err = conn.ReadMessage()
		if err == nil {
			t.Fatal("Expected connection to be closed due to missing JWT")
		}
	})

	t.Run("Expired JWT Token", func(t *testing.T) {
		// 生成已过期的JWT token
		deviceID := "test-device-expired"
		tags := []string{"user", "mobile"}
		expiredToken, err := jwtManager.GenerateToken(deviceID, tags, -time.Hour) // 负数表示已过期
		if err != nil {
			t.Fatalf("Failed to generate expired token: %v", err)
		}

		// 连接WebSocket
		conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
		if err != nil {
			t.Fatalf("Failed to connect: %v", err)
		}
		defer conn.Close()

		// 发送带过期token的握手消息
		handshake := protocol.HandshakePayload{
			DeviceID:   deviceID,
			Tags:       tags,
			ClientInfo: "test-client",
			JWTToken:   expiredToken,
		}

		handshakeJSON, _ := json.Marshal(handshake)
		handshakeMsg := protocol.NewMessage(protocol.MsgTypeHandshake, handshakeJSON)
		handshakeData, _ := handshakeMsg.Marshal()

		err = conn.WriteMessage(websocket.BinaryMessage, handshakeData)
		if err != nil {
			t.Fatalf("Failed to send handshake: %v", err)
		}

		// 连接应该被关闭
		conn.SetReadDeadline(time.Now().Add(5 * time.Second))
		_, _, err = conn.ReadMessage()
		if err == nil {
			t.Fatal("Expected connection to be closed due to expired JWT")
		}
	})

	t.Run("JWT Token with Device ID Mismatch", func(t *testing.T) {
		// 生成包含特定设备ID的JWT token
		tokenDeviceID := "token-device-123"
		handshakeDeviceID := "handshake-device-456"
		tags := []string{"user", "mobile"}
		token, err := jwtManager.GenerateToken(tokenDeviceID, tags)
		if err != nil {
			t.Fatalf("Failed to generate token: %v", err)
		}

		// 连接WebSocket
		conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
		if err != nil {
			t.Fatalf("Failed to connect: %v", err)
		}
		defer conn.Close()

		// 发送握手消息，设备ID与token中的不匹配
		handshake := protocol.HandshakePayload{
			DeviceID:   handshakeDeviceID,
			Tags:       tags,
			ClientInfo: "test-client",
			JWTToken:   token,
		}

		handshakeJSON, _ := json.Marshal(handshake)
		handshakeMsg := protocol.NewMessage(protocol.MsgTypeHandshake, handshakeJSON)
		handshakeData, _ := handshakeMsg.Marshal()

		err = conn.WriteMessage(websocket.BinaryMessage, handshakeData)
		if err != nil {
			t.Fatalf("Failed to send handshake: %v", err)
		}

		// 连接应该被关闭
		conn.SetReadDeadline(time.Now().Add(5 * time.Second))
		_, _, err = conn.ReadMessage()
		if err == nil {
			t.Fatal("Expected connection to be closed due to device ID mismatch")
		}
	})
}

func TestConnectionManager_JWT_Disabled(t *testing.T) {
	// 创建设备管理器
	deviceManager := device.NewDeviceManager("device-secret")

	// 创建连接管理器（禁用JWT）
	logger, _ := zap.NewDevelopment()
	cm := NewConnectionManager(
		deviceManager,
		nil, // JWT管理器为nil
		logger,
		60*time.Second,
		10*time.Second,
		30*time.Second,
		1024*1024,
		false, // 禁用JWT
	)

	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		cm.HandleWebSocket(w, r)
	}))
	defer server.Close()

	// 将http://转换为ws://
	wsURL := "ws" + strings.TrimPrefix(server.URL, "http")

	t.Run("Connection without JWT when disabled", func(t *testing.T) {
		// 连接WebSocket
		conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
		if err != nil {
			t.Fatalf("Failed to connect: %v", err)
		}
		defer conn.Close()

		// 发送不带JWT token的握手消息
		handshake := protocol.HandshakePayload{
			Tags:       []string{"user", "mobile"},
			ClientInfo: "test-client",
			// 不设置JWTToken
		}

		handshakeJSON, _ := json.Marshal(handshake)
		handshakeMsg := protocol.NewMessage(protocol.MsgTypeHandshake, handshakeJSON)
		handshakeData, _ := handshakeMsg.Marshal()

		err = conn.WriteMessage(websocket.BinaryMessage, handshakeData)
		if err != nil {
			t.Fatalf("Failed to send handshake: %v", err)
		}

		// 读取握手响应
		_, responseData, err := conn.ReadMessage()
		if err != nil {
			t.Fatalf("Failed to read handshake response: %v", err)
		}

		var responseMsg protocol.Message
		err = responseMsg.Unmarshal(responseData)
		if err != nil {
			t.Fatalf("Failed to unmarshal response: %v", err)
		}

		if responseMsg.GetType() != protocol.MsgTypeHandshakeResp {
			t.Fatalf("Expected handshake response, got %v", responseMsg.GetType())
		}

		var response map[string]interface{}
		err = json.Unmarshal(responseMsg.GetPayload(), &response)
		if err != nil {
			t.Fatalf("Failed to unmarshal response payload: %v", err)
		}

		if response["status"] != "connected" {
			t.Errorf("Expected status 'connected', got %v", response["status"])
		}

		// 应该生成了设备ID
		if response["device_id"] == nil || response["device_id"] == "" {
			t.Error("Expected device_id to be generated")
		}
	})
}
